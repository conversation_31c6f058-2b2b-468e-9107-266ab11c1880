# HiddenGov - 美国政府采购信息服务平台

专业的美国政府采购信息查询、分析和导出服务平台，基于 Next.js 15 和 Supabase 构建。

## 🚀 功能特性

- **高效查询**: 突破 SAM.gov 原生界面限制，提供灵活的数据检索
- **数据分析**: 整合分散的政府采购信息，形成统一的数据视图
- **权限管理**: 基于邀请码的封闭注册系统，支持多角色权限控制
- **数据导出**: 支持 CSV 格式数据导出，满足业务分析需求
- **响应式设计**: 支持桌面和移动设备，提供一致的用户体验

## 🛠 技术栈

- **前端**: Next.js 15 (App Router), TypeScript, Tailwind CSS
- **UI 组件**: shadcn-ui, Radix UI
- **后端**: Supabase (PostgreSQL, Auth, RLS)
- **状态管理**: Zustand
- **表单处理**: React Hook Form + Zod
- **包管理**: pnpm

## 📋 环境要求

- Node.js 18.17 或更高版本
- pnpm 8.0 或更高版本
- Supabase 项目

## 🔧 安装和配置

### 1. 克隆项目

```bash
git clone <repository-url>
cd hiddengov2
```

### 2. 安装依赖

```bash
pnpm install
```

### 3. 环境变量配置

复制环境变量模板：

```bash
cp .env.example .env.local
```

在 `.env.local` 中填入以下配置：

```env
# Supabase 配置
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_KEY=your_supabase_service_key_here

# Cloudflare Turnstile 配置
NEXT_PUBLIC_TURNSTILE_SITE_KEY=your_turnstile_site_key_here
TURNSTILE_SECRET_KEY=your_turnstile_secret_key_here

# 应用配置
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### 4. 数据库设置

在 Supabase 中执行 `docs/sql/` 目录下的 SQL 文件来创建必要的表和视图：

```sql
-- 按顺序执行以下文件：
-- 1. us_sam_u_profiles.sql
-- 2. us_sam_u_invite_codes.sql
-- 3. us_sam_u_logs.sql
-- 4. us_sam_gov_project_view.sql
-- 5. us_sam_gov_project_detail_view.sql
-- 6. us_sam_gov_stats.sql
```

### 5. 启动开发服务器

```bash
pnpm dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

## 📁 项目结构

```
src/
├── app/                    # Next.js App Router
│   ├── (auth)/            # 认证页面组
│   ├── (dashboard)/       # 主应用页面组
│   ├── admin/             # 管理后台
│   └── api/               # API 路由
├── components/            # 可复用组件
│   ├── ui/               # shadcn-ui 组件
│   ├── features/         # 业务组件
│   └── layouts/          # 布局组件
├── lib/                  # 工具库
│   ├── supabase/        # Supabase 客户端
│   ├── validations/     # Zod schemas
│   └── utils/           # 工具函数
├── hooks/               # 自定义 Hooks
├── stores/              # 状态管理
└── types/               # TypeScript 类型
```

## 🔐 用户角色

- **USER**: 普通用户，可查看项目列表和详情，使用搜索筛选，导出数据
- **ADMIN**: 管理员，拥有普通用户权限 + 用户管理、邀请码管理、系统日志查看
- **SUPER**: 超级管理员，拥有所有权限 + 系统配置管理

## 🚀 部署

### Vercel 部署

1. 将代码推送到 GitHub
2. 在 Vercel 中导入项目
3. 配置环境变量
4. 部署

### 环境变量检查清单

- [ ] `NEXT_PUBLIC_SUPABASE_URL`
- [ ] `NEXT_PUBLIC_SUPABASE_ANON_KEY`
- [ ] `SUPABASE_SERVICE_KEY`
- [ ] `NEXT_PUBLIC_TURNSTILE_SITE_KEY`
- [ ] `TURNSTILE_SECRET_KEY`
- [ ] `NEXT_PUBLIC_APP_URL`

## 📝 开发指南

### 代码规范

```bash
# 代码检查
pnpm lint

# 自动修复
pnpm lint:fix

# 类型检查
pnpm type-check

# 代码格式化
pnpm format
```

### Git 提交规范

遵循 Conventional Commits：

- `feat: 添加新功能`
- `fix: 修复问题`
- `docs: 更新文档`
- `style: 代码格式调整`
- `refactor: 代码重构`
- `test: 添加测试`
- `chore: 构建过程或辅助工具的变动`

## 🔍 故障排除

### 常见问题

1. **Supabase 连接失败**
   - 检查环境变量是否正确配置
   - 确认 Supabase 项目状态正常

2. **权限错误**
   - 检查 RLS 策略是否正确设置
   - 确认用户角色和权限配置

3. **构建失败**
   - 运行 `pnpm type-check` 检查类型错误
   - 确保所有依赖正确安装

## 📄 许可证

本项目采用 MIT 许可证。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 支持

如有问题，请联系开发团队或查看项目文档。

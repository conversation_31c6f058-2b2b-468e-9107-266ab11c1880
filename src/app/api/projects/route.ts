// 项目列表 API
import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { projectFilterSchema } from '@/lib/validations/project'

export async function GET(request: NextRequest) {
  try {
    // 解析查询参数
    const searchParams = Object.fromEntries(request.nextUrl.searchParams)
    const filters = projectFilterSchema.parse(searchParams)

    // 检查认证
    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: { code: 'AUTH001', message: '未授权访问' } },
        { status: 401 }
      )
    }

    // 检查用户状态
    const { data: profile, error: profileError } = await supabase
      .from('us_sam_u_profiles')
      .select('is_active, subscription_end')
      .eq('auth_user_id', user.id)
      .single()

    if (profileError || !profile || !profile.is_active) {
      return NextResponse.json(
        { success: false, error: { code: 'AUTH003', message: '账户未激活' } },
        { status: 403 }
      )
    }

    // 构建查询
    let query = supabase
      .from('us_sam_gov_project_view')
      .select('*', { count: 'exact' })

    // 应用筛选条件
    if (filters.keyword) {
      query = query.or(`title.ilike.%${filters.keyword}%,solicitationnumber.ilike.%${filters.keyword}%`)
    }

    if (filters.type) {
      query = query.eq('type', filters.type)
    }

    if (filters.dateFrom) {
      query = query.gte('posted_date', filters.dateFrom)
    }

    if (filters.dateTo) {
      query = query.lte('posted_date', filters.dateTo)
    }

    if (filters.hasResources !== undefined) {
      query = query.eq('has_resources', filters.hasResources)
    }

    if (filters.agency) {
      query = query.ilike('fullparentpathname', `%${filters.agency}%`)
    }

    // 应用分页和排序
    const { data, error, count } = await query
      .order('posted_date', { ascending: false })
      .range(
        (filters.page - 1) * filters.pageSize,
        filters.page * filters.pageSize - 1
      )

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json(
        { success: false, error: { code: 'SYS001', message: '数据库查询失败' } },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data,
      pagination: {
        page: filters.page,
        pageSize: filters.pageSize,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / filters.pageSize),
      },
      metadata: {
        timestamp: new Date().toISOString(),
        requestId: crypto.randomUUID(),
      },
    })
  } catch (error) {
    console.error('API Error:', error)

    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'SYS003',
            message: '请求参数格式错误',
            details: error.message
          }
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      {
        success: false,
        error: { code: 'SYS002', message: '服务器内部错误' }
      },
      { status: 500 }
    )
  }
}

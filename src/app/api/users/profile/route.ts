// 用户档案管理 API
import { NextRequest, NextResponse } from 'next/server'
import { createClient, createAdminClient } from '@/lib/supabase/server'
import { userProfileSchema } from '@/lib/validations/user'

// 获取用户档案
export async function GET() {
  try {
    const supabase = await createClient()

    // 获取当前用户
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'AUTH001',
            message: '未授权访问'
          }
        },
        { status: 401 }
      )
    }

    // 获取用户档案
    const { data: profile, error: profileError } = await supabase
      .from('us_sam_u_profiles')
      .select('*')
      .eq('auth_user_id', user.id)
      .single()

    if (profileError || !profile) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'AUTH002',
            message: '用户档案不存在'
          }
        },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        id: profile.id,
        authUserId: profile.auth_user_id,
        email: profile.email,
        fullName: profile.full_name,
        role: profile.role,
        isActive: profile.is_active,
        subscriptionStart: profile.subscription_start,
        subscriptionEnd: profile.subscription_end,
        inviteCodeUsed: profile.invite_code_used,
        createdAt: profile.created_at,
        updatedAt: profile.updated_at,
      },
      metadata: {
        timestamp: new Date().toISOString(),
        requestId: crypto.randomUUID(),
      },
    })

  } catch (error) {
    console.error('Get profile API error:', error)

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'SYS002',
          message: '服务器内部错误'
        }
      },
      { status: 500 }
    )
  }
}

// 更新用户档案
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()

    // 验证输入数据
    const validatedData = userProfileSchema.parse(body)

    const supabase = await createClient()

    // 获取当前用户
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'AUTH001',
            message: '未授权访问'
          }
        },
        { status: 401 }
      )
    }

    // 获取客户端 IP
    const ip = request.headers.get('x-forwarded-for') ||
               request.headers.get('x-real-ip') ||
               'unknown'

    // 更新用户档案
    const { data: updatedProfile, error: updateError } = await supabase
      .from('us_sam_u_profiles')
      .update({
        full_name: validatedData.fullName,
        email: validatedData.email,
        updated_at: new Date().toISOString(),
      })
      .eq('auth_user_id', user.id)
      .select()
      .single()

    if (updateError) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'SYS001',
            message: '更新用户档案失败'
          }
        },
        { status: 500 }
      )
    }

    // 如果邮箱发生变化，更新 Auth 用户信息
    if (validatedData.email !== user.email) {
      const adminSupabase = await createAdminClient()
      await adminSupabase.auth.admin.updateUserById(user.id, {
        email: validatedData.email,
      })
    }

    // 记录更新日志
    const adminSupabase = await createAdminClient()
    await adminSupabase
      .from('us_sam_u_logs')
      .insert({
        auth_user_id: user.id,
        event_type: 'USER_PROFILE_UPDATE',
        event_data: {
          old_email: user.email,
          new_email: validatedData.email,
          full_name: validatedData.fullName,
          ip_address: ip,
        },
        ip_address: ip,
        user_agent: request.headers.get('user-agent') || '',
      })

    return NextResponse.json({
      success: true,
      data: {
        id: updatedProfile.id,
        authUserId: updatedProfile.auth_user_id,
        email: updatedProfile.email,
        fullName: updatedProfile.full_name,
        role: updatedProfile.role,
        isActive: updatedProfile.is_active,
        subscriptionStart: updatedProfile.subscription_start,
        subscriptionEnd: updatedProfile.subscription_end,
        updatedAt: updatedProfile.updated_at,
      },
      metadata: {
        timestamp: new Date().toISOString(),
        requestId: crypto.randomUUID(),
      },
    })

  } catch (error) {
    console.error('Update profile API error:', error)

    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'SYS003',
            message: '请求参数格式错误',
            details: error.message
          }
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'SYS002',
          message: '服务器内部错误'
        }
      },
      { status: 500 }
    )
  }
}

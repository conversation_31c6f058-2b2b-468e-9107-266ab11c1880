// 用户注册 API
import { NextRequest, NextResponse } from 'next/server'
import { createAdminClient } from '@/lib/supabase/server'
import { registerSchema } from '@/lib/validations/auth'

// Turnstile 验证函数
async function verifyTurnstile(token: string, ip?: string): Promise<boolean> {
  if (!process.env.TURNSTILE_SECRET_KEY) {
    console.warn('TURNSTILE_SECRET_KEY not configured')
    return true // 开发环境跳过验证
  }

  try {
    const response = await fetch('https://challenges.cloudflare.com/turnstile/v0/siteverify', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        secret: process.env.TURNSTILE_SECRET_KEY,
        response: token,
        remoteip: ip || '',
      }),
    })

    const result = await response.json()
    return result.success === true
  } catch (error) {
    console.error('Turnstile verification error:', error)
    return false
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // 验证输入数据
    const validatedData = registerSchema.parse(body)

    // 获取客户端 IP
    const ip = request.headers.get('x-forwarded-for') ||
               request.headers.get('x-real-ip') ||
               'unknown'

    // 验证 Turnstile token
    const isTurnstileValid = await verifyTurnstile(validatedData.turnstileToken, ip)
    if (!isTurnstileValid) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'AUTH004',
            message: '人机验证失败，请重试'
          }
        },
        { status: 400 }
      )
    }

    // 使用管理员客户端进行操作
    const adminSupabase = await createAdminClient()

    // 验证邀请码
    const { data: inviteCodeData, error: inviteError } = await adminSupabase
      .from('us_sam_u_invite_codes')
      .select('*')
      .eq('code', validatedData.inviteCode)
      .eq('is_active', true)
      .single()

    if (inviteError || !inviteCodeData) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'BIZ001',
            message: '邀请码无效或已过期'
          }
        },
        { status: 400 }
      )
    }

    // 检查邀请码使用次数
    if (inviteCodeData.current_uses >= inviteCodeData.max_uses) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'BIZ002',
            message: '邀请码使用次数已达上限'
          }
        },
        { status: 400 }
      )
    }

    // 检查邀请码是否过期
    if (inviteCodeData.expires_at && new Date(inviteCodeData.expires_at) < new Date()) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'BIZ002',
            message: '邀请码已过期'
          }
        },
        { status: 400 }
      )
    }

    // 注册用户
    const { data: authData, error: authError } = await adminSupabase.auth.admin.createUser({
      email: validatedData.email,
      password: validatedData.password,
      user_metadata: {
        full_name: validatedData.fullName,
      },
      email_confirm: false, // 自动确认邮箱
    })

    if (authError || !authData.user) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'AUTH005',
            message: authError?.message || '用户创建失败'
          }
        },
        { status: 400 }
      )
    }

    // 创建用户档案
    const { error: profileError } = await adminSupabase
      .from('us_sam_u_profiles')
      .insert({
        auth_user_id: authData.user.id,
        email: validatedData.email,
        full_name: validatedData.fullName,
        invite_code_used: validatedData.inviteCode,
        subscription_start: new Date().toISOString(),
        subscription_end: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7天试用
      })

    if (profileError) {
      // 如果档案创建失败，删除已创建的用户
      await adminSupabase.auth.admin.deleteUser(authData.user.id)

      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'SYS001',
            message: '用户档案创建失败'
          }
        },
        { status: 500 }
      )
    }

    // 更新邀请码使用次数
    await adminSupabase
      .from('us_sam_u_invite_codes')
      .update({
        current_uses: inviteCodeData.current_uses + 1,
        updated_at: new Date().toISOString()
      })
      .eq('id', inviteCodeData.id)

    // 记录注册日志
    await adminSupabase
      .from('us_sam_u_logs')
      .insert({
        auth_user_id: authData.user.id,
        event_type: 'USER_REGISTER',
        event_data: {
          email: validatedData.email,
          invite_code: validatedData.inviteCode,
          ip_address: ip,
        },
        ip_address: ip,
        user_agent: request.headers.get('user-agent') || '',
      })

    return NextResponse.json({
      success: true,
      data: {
        user: {
          id: authData.user.id,
          email: authData.user.email,
          fullName: validatedData.fullName,
        },
      },
      metadata: {
        timestamp: new Date().toISOString(),
        requestId: crypto.randomUUID(),
      },
    })

  } catch (error) {
    console.error('Registration API error:', error)

    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'SYS003',
            message: '请求参数格式错误',
            details: error.message
          }
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'SYS002',
          message: '服务器内部错误'
        }
      },
      { status: 500 }
    )
  }
}

// 用户登出 API
import { NextRequest, NextResponse } from 'next/server'
import { createClient, createAdminClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    // 获取客户端 IP
    const ip = request.headers.get('x-forwarded-for') ||
               request.headers.get('x-real-ip') ||
               'unknown'

    // 创建 Supabase 客户端
    const supabase = await createClient()

    // 获取当前用户
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError || !user) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'AUTH001',
            message: '未找到有效的用户会话'
          }
        },
        { status: 401 }
      )
    }

    // 执行登出
    const { error: signOutError } = await supabase.auth.signOut()

    if (signOutError) {
      console.error('Sign out error:', signOutError)
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'AUTH006',
            message: '登出失败，请重试'
          }
        },
        { status: 500 }
      )
    }

    // 记录登出日志
    const adminSupabase = await createAdminClient()
    await adminSupabase
      .from('us_sam_u_logs')
      .insert({
        auth_user_id: user.id,
        event_type: 'USER_LOGOUT',
        event_data: {
          email: user.email,
          ip_address: ip,
        },
        ip_address: ip,
        user_agent: request.headers.get('user-agent') || '',
      })

    return NextResponse.json({
      success: true,
      data: {
        message: '登出成功',
      },
      metadata: {
        timestamp: new Date().toISOString(),
        requestId: crypto.randomUUID(),
      },
    })

  } catch (error) {
    console.error('Logout API error:', error)

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'SYS002',
          message: '服务器内部错误'
        }
      },
      { status: 500 }
    )
  }
}

// 邀请码验证 API
import { NextRequest, NextResponse } from 'next/server'
import { createAdminClient } from '@/lib/supabase/server'
import { inviteCodeSchema } from '@/lib/validations/auth'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // 验证输入数据
    const validatedData = inviteCodeSchema.parse(body)

    // 使用管理员客户端查询邀请码
    const adminSupabase = await createAdminClient()

    const { data: inviteCodeData, error: inviteError } = await adminSupabase
      .from('us_sam_u_invite_codes')
      .select('*')
      .eq('code', validatedData.code)
      .eq('is_active', true)
      .single()

    if (inviteError || !inviteCodeData) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'BIZ001',
            message: '邀请码无效或已过期'
          }
        },
        { status: 400 }
      )
    }

    // 检查邀请码使用次数
    if (inviteCodeData.current_uses >= inviteCodeData.max_uses) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'BIZ002',
            message: '邀请码使用次数已达上限'
          }
        },
        { status: 400 }
      )
    }

    // 检查邀请码是否过期
    if (inviteCodeData.expires_at && new Date(inviteCodeData.expires_at) < new Date()) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'BIZ002',
            message: '邀请码已过期'
          }
        },
        { status: 400 }
      )
    }

    // 获取创建者信息
    const { data: creatorProfile } = await adminSupabase
      .from('us_sam_u_profiles')
      .select('full_name, email')
      .eq('auth_user_id', inviteCodeData.created_by)
      .single()

    return NextResponse.json({
      success: true,
      data: {
        code: inviteCodeData.code,
        maxUses: inviteCodeData.max_uses,
        currentUses: inviteCodeData.current_uses,
        remainingUses: inviteCodeData.max_uses - inviteCodeData.current_uses,
        expiresAt: inviteCodeData.expires_at,
        createdBy: creatorProfile?.full_name || 'Unknown',
        createdAt: inviteCodeData.created_at,
        isValid: true,
      },
      metadata: {
        timestamp: new Date().toISOString(),
        requestId: crypto.randomUUID(),
      },
    })

  } catch (error) {
    console.error('Validate code API error:', error)

    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'SYS003',
            message: '请求参数格式错误',
            details: error.message
          }
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'SYS002',
          message: '服务器内部错误'
        }
      },
      { status: 500 }
    )
  }
}

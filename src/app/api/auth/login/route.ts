// 用户登录 API
import { NextRequest, NextResponse } from 'next/server'
import { createClient, createAdminClient } from '@/lib/supabase/server'
import { loginSchema } from '@/lib/validations/auth'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // 验证输入数据
    const validatedData = loginSchema.parse(body)

    // 获取客户端 IP
    const ip = request.headers.get('x-forwarded-for') ||
               request.headers.get('x-real-ip') ||
               'unknown'

    // 创建 Supabase 客户端
    const supabase = await createClient()

    // 尝试登录
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: validatedData.email,
      password: validatedData.password,
    })

    if (authError || !authData.user) {
      // 记录登录失败日志
      const adminSupabase = await createAdminClient()
      await adminSupabase
        .from('us_sam_u_logs')
        .insert({
          event_type: 'USER_LOGIN_FAILED',
          event_data: {
            email: validatedData.email,
            error: authError?.message || 'Unknown error',
            ip_address: ip,
          },
          ip_address: ip,
          user_agent: request.headers.get('user-agent') || '',
        })

      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'AUTH001',
            message: '邮箱或密码错误'
          }
        },
        { status: 401 }
      )
    }

    // 获取用户档案
    const { data: profile, error: profileError } = await supabase
      .from('us_sam_u_profiles')
      .select('*')
      .eq('auth_user_id', authData.user.id)
      .single()

    if (profileError || !profile) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'AUTH002',
            message: '用户档案不存在'
          }
        },
        { status: 404 }
      )
    }

    // 检查用户是否激活
    if (!profile.is_active) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'AUTH003',
            message: '账户已被禁用，请联系管理员'
          }
        },
        { status: 403 }
      )
    }

    // 检查订阅是否过期
    if (profile.subscription_end) {
      const subscriptionEnd = new Date(profile.subscription_end)
      const now = new Date()
      if (now > subscriptionEnd) {
        return NextResponse.json(
          {
            success: false,
            error: {
              code: 'BIZ003',
              message: '订阅已过期，请联系管理员续费'
            }
          },
          { status: 403 }
        )
      }
    }

    // 记录登录成功日志
    const adminSupabase = await createAdminClient()
    await adminSupabase
      .from('us_sam_u_logs')
      .insert({
        auth_user_id: authData.user.id,
        event_type: 'USER_LOGIN_SUCCESS',
        event_data: {
          email: validatedData.email,
          ip_address: ip,
          remember_me: validatedData.rememberMe || false,
        },
        ip_address: ip,
        user_agent: request.headers.get('user-agent') || '',
      })

    return NextResponse.json({
      success: true,
      data: {
        user: {
          id: authData.user.id,
          email: authData.user.email,
          fullName: profile.full_name,
          role: profile.role,
          isActive: profile.is_active,
          subscriptionEnd: profile.subscription_end,
        },
        session: {
          accessToken: authData.session?.access_token,
          refreshToken: authData.session?.refresh_token,
          expiresAt: authData.session?.expires_at,
        },
      },
      metadata: {
        timestamp: new Date().toISOString(),
        requestId: crypto.randomUUID(),
      },
    })

  } catch (error) {
    console.error('Login API error:', error)

    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'SYS003',
            message: '请求参数格式错误',
            details: error.message
          }
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'SYS002',
          message: '服务器内部错误'
        }
      },
      { status: 500 }
    )
  }
}

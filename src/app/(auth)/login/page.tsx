// 登录页面
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { LoginForm } from "@/components/features/auth/login-form"

export default function LoginPage() {
  return (
    <Card>
      <CardHeader className="text-center">
        <CardTitle className="text-2xl font-bold">登录 HiddenGov</CardTitle>
        <CardDescription>
          使用您的账户登录访问政府采购信息
        </CardDescription>
      </CardHeader>
      <CardContent>
        <LoginForm />
        <div className="mt-6 text-center text-sm">
          <span className="text-gray-600">还没有账户？</span>{" "}
          <Link href="/register" className="text-blue-600 hover:underline">
            申请注册
          </Link>
        </div>
      </CardContent>
    </Card>
  )
}

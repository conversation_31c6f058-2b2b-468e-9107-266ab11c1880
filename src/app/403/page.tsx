// 403 权限不足页面
import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

export default function ForbiddenPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <Card className="w-full max-w-md text-center">
        <CardHeader>
          <div className="text-6xl font-bold text-red-600 mb-4">403</div>
          <CardTitle>权限不足</CardTitle>
          <CardDescription>
            抱歉，您没有权限访问此页面，请联系管理员
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button asChild className="w-full">
            <Link href="/projects">返回项目列表</Link>
          </Button>
          <Button asChild variant="outline" className="w-full">
            <Link href="/login">重新登录</Link>
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}

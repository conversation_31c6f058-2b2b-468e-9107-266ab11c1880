// 项目列表页面
import { Suspense } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ProjectList } from "@/components/features/projects/project-list"
import { ProjectFilters } from "@/components/features/projects/project-filters"

export default function ProjectsPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">项目列表</h1>
        <p className="text-gray-600 mt-2">
          查看和搜索美国政府采购项目信息
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* 筛选面板 */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle>筛选条件</CardTitle>
              <CardDescription>
                使用筛选条件缩小搜索范围
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ProjectFilters />
            </CardContent>
          </Card>
        </div>

        {/* 项目列表 */}
        <div className="lg:col-span-3">
          <Suspense fallback={<div>加载中...</div>}>
            <ProjectList />
          </Suspense>
        </div>
      </div>
    </div>
  )
}

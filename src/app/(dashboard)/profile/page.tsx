// 个人中心页面
import { Suspense } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ProfileForm } from "@/components/features/users/profile-form"
import { ProfileInfo } from "@/components/features/users/profile-info"

export default function ProfilePage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">个人中心</h1>
        <p className="text-gray-600 mt-2">
          管理您的账户信息和设置
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 账户信息 */}
        <Card>
          <CardHeader>
            <CardTitle>账户信息</CardTitle>
            <CardDescription>
              查看您的账户详细信息
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Suspense fallback={<div>加载中...</div>}>
              <ProfileInfo />
            </Suspense>
          </CardContent>
        </Card>

        {/* 编辑档案 */}
        <Card>
          <CardHeader>
            <CardTitle>编辑档案</CardTitle>
            <CardDescription>
              更新您的个人信息
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Suspense fallback={<div>加载中...</div>}>
              <ProfileForm />
            </Suspense>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

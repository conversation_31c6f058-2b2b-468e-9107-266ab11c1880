import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-16">
        {/* Header */}
        <header className="text-center mb-16">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            HiddenGov
          </h1>
          <p className="text-xl md:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto">
            专业的美国政府采购信息服务平台
          </p>
          <p className="text-lg text-gray-500 mb-12 max-w-2xl mx-auto">
            突破 SAM.gov 原生界面限制，为企业提供高效的政府采购信息查询、分析和导出服务
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg" className="text-lg px-8 py-3">
              <Link href="/login">立即登录</Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="text-lg px-8 py-3">
              <Link href="/register">申请试用</Link>
            </Button>
          </div>
        </header>

        {/* Features */}
        <section className="grid md:grid-cols-3 gap-8 mb-16">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <span className="text-2xl">🔍</span>
                高效查询
              </CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription>
                突破 SAM.gov 原生界面的查询限制，提供更灵活的数据检索和筛选功能
              </CardDescription>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <span className="text-2xl">📊</span>
                数据分析
              </CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription>
                整合分散的政府采购信息，形成统一的数据视图，支持多维度分析
              </CardDescription>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <span className="text-2xl">📈</span>
                商业情报
              </CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription>
                为企业参与美国政府采购提供情报支持和决策依据，把握商机
              </CardDescription>
            </CardContent>
          </Card>
        </section>

        {/* CTA */}
        <section className="text-center">
          <Card className="max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle className="text-2xl">开始使用 HiddenGov</CardTitle>
              <CardDescription className="text-lg">
                需要邀请码才能注册，请联系我们获取试用权限
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button asChild size="lg" className="w-full sm:w-auto">
                <Link href="/register">申请邀请码</Link>
              </Button>
            </CardContent>
          </Card>
        </section>
      </div>
    </div>
  );
}

// 常量定义

// 用户角色
export const USER_ROLES = {
  USER: 'USER',
  ADMIN: 'ADMIN',
  SUPER: 'SUPER',
} as const

// 项目类型
export const PROJECT_TYPES = {
  CONTRACT: 'CONTRACT',
  GRANT: 'GRANT',
  OTHER: 'OTHER',
} as const

// 分页配置
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 20,
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100],
  MAX_PAGE_SIZE: 100,
} as const

// 导出配置
export const EXPORT = {
  MAX_RECORDS: 10000,
  FORMATS: ['csv'] as const,
  CSV_HEADERS: {
    id: 'ID',
    solicitationnumber: '项目编号',
    title: '项目名称',
    type: '项目类型',
    fullparentpathname: '采购机构',
    posted_date: '发布日期',
    response_deadline: '截止日期',
    poc_fullname: '联系人',
    awardee_name: '中标方',
    has_resources: '是否有资源',
  },
} as const

// 验证配置
export const VALIDATION = {
  PASSWORD_MIN_LENGTH: 6,
  NAME_MIN_LENGTH: 2,
  NAME_MAX_LENGTH: 50,
  INVITE_CODE_MIN_LENGTH: 6,
  SEARCH_MAX_LENGTH: 200,
} as const

// 会话配置
export const SESSION = {
  REMEMBER_ME_DURATION: 30 * 24 * 60 * 60 * 1000, // 30天
  DEFAULT_DURATION: 24 * 60 * 60 * 1000, // 24小时
  REFRESH_THRESHOLD: 60 * 60 * 1000, // 1小时
} as const

// API 配置
export const API = {
  RATE_LIMIT: {
    REQUESTS_PER_MINUTE: 60,
    REQUESTS_PER_HOUR: 1000,
  },
  TIMEOUT: 30000, // 30秒
} as const

// 主题配置
export const THEME = {
  LIGHT: 'light',
  DARK: 'dark',
  SYSTEM: 'system',
} as const

// 错误消息
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络连接失败，请检查网络后重试',
  SERVER_ERROR: '服务器错误，请稍后重试',
  UNAUTHORIZED: '未授权访问，请重新登录',
  FORBIDDEN: '权限不足，无法执行此操作',
  NOT_FOUND: '请求的资源不存在',
  VALIDATION_ERROR: '输入数据格式不正确',
  RATE_LIMIT_EXCEEDED: '请求过于频繁，请稍后重试',
} as const

// 成功消息
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: '登录成功',
  REGISTER_SUCCESS: '注册成功',
  LOGOUT_SUCCESS: '退出成功',
  UPDATE_SUCCESS: '更新成功',
  DELETE_SUCCESS: '删除成功',
  EXPORT_SUCCESS: '导出成功',
  INVITE_CODE_CREATED: '邀请码创建成功',
} as const

// 路由路径
export const ROUTES = {
  HOME: '/',
  LOGIN: '/login',
  REGISTER: '/register',
  DASHBOARD: '/projects',
  PROJECTS: '/projects',
  PROFILE: '/profile',
  ADMIN: '/admin',
  ADMIN_USERS: '/admin/users',
  ADMIN_INVITE_CODES: '/admin/invite-codes',
  ADMIN_LOGS: '/admin/logs',
} as const

// 本地存储键
export const STORAGE_KEYS = {
  THEME: 'hiddengov-theme',
  SIDEBAR_OPEN: 'hiddengov-sidebar-open',
  FILTERS: 'hiddengov-filters',
} as const

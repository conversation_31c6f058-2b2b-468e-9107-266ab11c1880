// 项目相关验证模式
import { z } from 'zod'

// 项目筛选验证
export const projectFilterSchema = z.object({
  page: z.coerce.number().min(1).default(1),
  pageSize: z.coerce.number().min(10).max(100).default(20),
  keyword: z.string().max(100).optional(),
  dateFrom: z.string().datetime().optional(),
  dateTo: z.string().datetime().optional(),
  type: z.enum(['CONTRACT', 'GRANT', 'OTHER']).optional(),
  hasResources: z.coerce.boolean().optional(),
  agency: z.string().max(200).optional(),
})

// 项目导出验证
export const projectExportSchema = z.object({
  filters: projectFilterSchema,
  fields: z.array(z.string()).min(1, '请选择至少一个导出字段'),
  format: z.enum(['csv']).default('csv'),
})

// 项目搜索验证
export const projectSearchSchema = z.object({
  query: z.string().min(1, '请输入搜索关键词').max(200, '搜索关键词不能超过200个字符'),
  filters: projectFilterSchema.optional(),
})

// 类型导出
export type ProjectFilters = z.infer<typeof projectFilterSchema>
export type ProjectExportData = z.infer<typeof projectExportSchema>
export type ProjectSearchData = z.infer<typeof projectSearchSchema>

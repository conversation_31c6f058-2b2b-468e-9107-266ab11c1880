// 用户相关验证模式
import { z } from 'zod'

// 用户档案更新验证
export const userProfileSchema = z.object({
  fullName: z
    .string()
    .min(1, '请输入姓名')
    .min(2, '姓名至少需要2个字符')
    .max(50, '姓名不能超过50个字符'),
  email: z
    .string()
    .min(1, '请输入邮箱')
    .email('请输入有效的邮箱地址'),
})

// 用户筛选验证
export const userFilterSchema = z.object({
  page: z.coerce.number().min(1).default(1),
  pageSize: z.coerce.number().min(10).max(100).default(20),
  role: z.enum(['USER', 'ADMIN', 'SUPER']).optional(),
  isActive: z.coerce.boolean().optional(),
  keyword: z.string().max(100).optional(),
})

// 用户状态更新验证
export const userStatusSchema = z.object({
  isActive: z.boolean(),
  subscriptionEnd: z.string().datetime().optional(),
})

// 用户角色更新验证
export const userRoleSchema = z.object({
  role: z.enum(['USER', 'ADMIN', 'SUPER']),
})

// 邀请码创建验证
export const createInviteCodeSchema = z.object({
  count: z.number().min(1, '数量至少为1').max(100, '单次最多创建100个'),
  maxUses: z.number().min(1).max(1000).default(1),
  expiresAt: z.string().datetime().optional(),
  prefix: z.string().max(10).optional(),
})

// 邀请码筛选验证
export const inviteCodeFilterSchema = z.object({
  page: z.coerce.number().min(1).default(1),
  pageSize: z.coerce.number().min(10).max(100).default(20),
  isActive: z.coerce.boolean().optional(),
  createdBy: z.string().uuid().optional(),
})

// 类型导出
export type UserProfileData = z.infer<typeof userProfileSchema>
export type UserFilters = z.infer<typeof userFilterSchema>
export type UserStatusData = z.infer<typeof userStatusSchema>
export type UserRoleData = z.infer<typeof userRoleSchema>
export type CreateInviteCodeData = z.infer<typeof createInviteCodeSchema>
export type InviteCodeFilters = z.infer<typeof inviteCodeFilterSchema>

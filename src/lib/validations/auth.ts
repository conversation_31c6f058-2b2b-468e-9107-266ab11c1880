// 认证相关验证模式
import { z } from 'zod'

// 登录表单验证
export const loginSchema = z.object({
  email: z
    .string()
    .min(1, '请输入邮箱')
    .email('请输入有效的邮箱地址'),
  password: z
    .string()
    .min(1, '请输入密码')
    .min(6, '密码至少需要6个字符'),
  rememberMe: z.boolean().optional(),
})

// 注册表单验证
export const registerSchema = z.object({
  email: z
    .string()
    .min(1, '请输入邮箱')
    .email('请输入有效的邮箱地址'),
  password: z
    .string()
    .min(1, '请输入密码')
    .min(6, '密码至少需要6个字符')
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      '密码必须包含大小写字母和数字'
    ),
  confirmPassword: z
    .string()
    .min(1, '请确认密码'),
  fullName: z
    .string()
    .min(1, '请输入姓名')
    .min(2, '姓名至少需要2个字符')
    .max(50, '姓名不能超过50个字符'),
  inviteCode: z
    .string()
    .min(1, '请输入邀请码')
    .min(6, '邀请码格式不正确'),
  turnstileToken: z
    .string()
    .min(1, '请完成人机验证'),
}).refine((data) => data.password === data.confirmPassword, {
  message: '两次输入的密码不一致',
  path: ['confirmPassword'],
})

// 邀请码验证
export const inviteCodeSchema = z.object({
  code: z
    .string()
    .min(1, '请输入邀请码')
    .min(6, '邀请码格式不正确'),
})

// 密码重置验证
export const resetPasswordSchema = z.object({
  email: z
    .string()
    .min(1, '请输入邮箱')
    .email('请输入有效的邮箱地址'),
})

// 修改密码验证
export const changePasswordSchema = z.object({
  currentPassword: z
    .string()
    .min(1, '请输入当前密码'),
  newPassword: z
    .string()
    .min(1, '请输入新密码')
    .min(6, '密码至少需要6个字符')
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      '密码必须包含大小写字母和数字'
    ),
  confirmNewPassword: z
    .string()
    .min(1, '请确认新密码'),
}).refine((data) => data.newPassword === data.confirmNewPassword, {
  message: '两次输入的密码不一致',
  path: ['confirmNewPassword'],
})

// 类型导出
export type LoginFormData = z.infer<typeof loginSchema>
export type RegisterFormData = z.infer<typeof registerSchema>
export type InviteCodeFormData = z.infer<typeof inviteCodeSchema>
export type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>
export type ChangePasswordFormData = z.infer<typeof changePasswordSchema>

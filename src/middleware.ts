// Next.js 中间件 - 处理认证和权限
import { NextResponse, type NextRequest } from 'next/server'
import { createClient } from '@/lib/supabase/server'

// 公开路径（无需认证）
const publicPaths = [
  '/',
  '/login',
  '/register',
  '/api/auth/register',
  '/api/auth/login',
  '/api/auth/validate-code',
]

// 管理员路径（需要管理员权限）
const adminPaths = [
  '/admin',
  '/admin/users',
  '/admin/invite-codes',
  '/admin/logs',
]

// API 管理员路径
const adminApiPaths = [
  '/api/users/list',
  '/api/invite-codes',
  '/api/stats',
]

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // 跳过静态文件和 Next.js 内部路径
  if (
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/favicon.ico') ||
    pathname.startsWith('/api/_') ||
    pathname.includes('.')
  ) {
    return NextResponse.next()
  }

  // 公开路径直接通过
  if (publicPaths.includes(pathname)) {
    return NextResponse.next()
  }

  try {
    // 创建 Supabase 客户端
    const supabase = await createClient()

    // 获取用户会话
    const { data: { user }, error } = await supabase.auth.getUser()

    if (error || !user) {
      // 未认证用户重定向到登录页
      const loginUrl = new URL('/login', request.url)
      loginUrl.searchParams.set('redirect', pathname)
      return NextResponse.redirect(loginUrl)
    }

    // 检查管理员权限
    const isAdminPath = adminPaths.some(path => pathname.startsWith(path))
    const isAdminApiPath = adminApiPaths.some(path => pathname.startsWith(path))

    if (isAdminPath || isAdminApiPath) {
      // 获取用户档案
      const { data: profile, error: profileError } = await supabase
        .from('us_sam_u_profiles')
        .select('role, is_active')
        .eq('auth_user_id', user.id)
        .single()

      if (profileError || !profile) {
        return NextResponse.redirect(new URL('/403', request.url))
      }

      // 检查用户是否激活
      if (!profile.is_active) {
        return NextResponse.redirect(new URL('/403', request.url))
      }

      // 检查管理员权限
      if (!['ADMIN', 'SUPER'].includes(profile.role)) {
        return NextResponse.redirect(new URL('/403', request.url))
      }
    }

    // 对于普通用户路径，检查用户是否激活
    if (!isAdminPath && !isAdminApiPath) {
      const { data: profile, error: profileError } = await supabase
        .from('us_sam_u_profiles')
        .select('is_active, subscription_end')
        .eq('auth_user_id', user.id)
        .single()

      if (profileError || !profile) {
        return NextResponse.redirect(new URL('/login', request.url))
      }

      // 检查用户是否激活
      if (!profile.is_active) {
        return NextResponse.redirect(new URL('/403', request.url))
      }

      // 检查订阅是否过期（如果有订阅结束时间）
      if (profile.subscription_end) {
        const subscriptionEnd = new Date(profile.subscription_end)
        const now = new Date()
        if (now > subscriptionEnd) {
          return NextResponse.redirect(new URL('/subscription-expired', request.url))
        }
      }
    }

    return NextResponse.next()
  } catch (error) {
    console.error('Middleware error:', error)
    return NextResponse.redirect(new URL('/login', request.url))
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}

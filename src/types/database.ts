// 数据库类型定义
export interface Database {
  public: {
    Tables: {
      us_sam_u_profiles: {
        Row: {
          id: string
          auth_user_id: string
          email: string
          full_name: string
          role: 'USER' | 'ADMIN' | 'SUPER'
          is_active: boolean
          subscription_start: string | null
          subscription_end: string | null
          invited_by: string | null
          invite_code_used: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          auth_user_id: string
          email: string
          full_name: string
          role?: 'USER' | 'ADMIN' | 'SUPER'
          is_active?: boolean
          subscription_start?: string | null
          subscription_end?: string | null
          invited_by?: string | null
          invite_code_used?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          auth_user_id?: string
          email?: string
          full_name?: string
          role?: 'USER' | 'ADMIN' | 'SUPER'
          is_active?: boolean
          subscription_start?: string | null
          subscription_end?: string | null
          invited_by?: string | null
          invite_code_used?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      us_sam_u_invite_codes: {
        Row: {
          id: string
          code: string
          created_by: string
          max_uses: number
          current_uses: number
          expires_at: string | null
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          code: string
          created_by: string
          max_uses?: number
          current_uses?: number
          expires_at?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          code?: string
          created_by?: string
          max_uses?: number
          current_uses?: number
          expires_at?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      us_sam_u_logs: {
        Row: {
          id: string
          auth_user_id: string | null
          event_type: string
          event_data: Record<string, unknown>
          ip_address: string | null
          user_agent: string | null
          event_time: string
        }
        Insert: {
          id?: string
          auth_user_id?: string | null
          event_type: string
          event_data?: Record<string, unknown>
          ip_address?: string | null
          user_agent?: string | null
          event_time?: string
        }
        Update: {
          id?: string
          auth_user_id?: string | null
          event_type?: string
          event_data?: Record<string, unknown>
          ip_address?: string | null
          user_agent?: string | null
          event_time?: string
        }
      }
    }
    Views: {
      us_sam_gov_project_view: {
        Row: {
          id: string
          solicitationnumber: string
          title: string
          type: string
          fullparentpathname: string
          posted_date: string
          response_deadline: string | null
          archive_date: string | null
          poc_fullname: string | null
          awardee_name: string | null
          has_resources: boolean
          formatted_posted_date: string
        }
      }
      us_sam_gov_project_detail_view: {
        Row: {
          id: string
          solicitationnumber: string
          title: string
          type: string
          fullparentpathname: string
          posted_date: string
          response_deadline: string | null
          archive_date: string | null
          detailed_description: string | null
          poc_fullname: string | null
          poc_email: string | null
          poc_phone: string | null
          awardee_name: string | null
          award_amount: number | null
          place_of_performance: string | null
          has_resources: boolean
          resource_links: string[] | null
        }
      }
      us_sam_gov_stats: {
        Row: {
          date: string
          new_projects_count: number
          total_projects_count: number
          contract_count: number
          grant_count: number
          other_count: number
        }
      }
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      user_role: 'USER' | 'ADMIN' | 'SUPER'
    }
  }
}

// 便捷类型别名
export type UserProfile = Database['public']['Tables']['us_sam_u_profiles']['Row']
export type InviteCode = Database['public']['Tables']['us_sam_u_invite_codes']['Row']
export type UserLog = Database['public']['Tables']['us_sam_u_logs']['Row']
export type Project = Database['public']['Views']['us_sam_gov_project_view']['Row']
export type ProjectDetail = Database['public']['Views']['us_sam_gov_project_detail_view']['Row']
export type ProjectStats = Database['public']['Views']['us_sam_gov_stats']['Row']

// 用户角色类型
export type UserRole = Database['public']['Enums']['user_role']

// API 响应类型定义

// 统一 API 响应格式
export interface ApiResponse<T = unknown> {
  success: boolean
  data?: T
  error?: {
    code: string
    message: string
    details?: unknown
  }
  metadata?: {
    timestamp: string
    requestId: string
  }
}

// 分页响应
export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
  }
}

// 错误代码枚举
export enum ErrorCode {
  // 认证错误
  AUTH_INVALID_CREDENTIALS = 'AUTH001',
  AUTH_SESSION_EXPIRED = 'AUTH002',
  AUTH_INSUFFICIENT_PERMISSIONS = 'AUTH003',

  // 业务错误
  INVITE_CODE_INVALID = 'BIZ001',
  INVITE_CODE_EXPIRED = 'BIZ002',
  USER_QUOTA_EXCEEDED = 'BIZ003',

  // 系统错误
  DATABASE_ERROR = 'SYS001',
  EXTERNAL_SERVICE_ERROR = 'SYS002',
  VALIDATION_ERROR = 'SYS003',
}

// 项目筛选参数
export interface ProjectFilters {
  page?: number
  pageSize?: number
  keyword?: string
  dateFrom?: string
  dateTo?: string
  type?: string
  hasResources?: boolean
  agency?: string
}

// 用户筛选参数
export interface UserFilters {
  page?: number
  pageSize?: number
  role?: string
  isActive?: boolean
  keyword?: string
}

// 邀请码创建参数
export interface CreateInviteCodeRequest {
  count: number
  maxUses?: number
  expiresAt?: string
  prefix?: string
}

// 用户注册参数
export interface RegisterRequest {
  email: string
  password: string
  fullName: string
  inviteCode: string
  turnstileToken: string
}

// 用户登录参数
export interface LoginRequest {
  email: string
  password: string
  rememberMe?: boolean
  turnstileToken?: string
}

// 导出请求参数
export interface ExportRequest {
  filters: ProjectFilters
  fields: string[]
  format: 'csv'
}

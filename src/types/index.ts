// 类型定义导出
export * from './database'
export * from './api'

// 通用类型
export interface User {
  id: string
  email: string
  fullName: string
  role: 'USER' | 'ADMIN' | 'SUPER'
  isActive: boolean
  subscriptionStart?: Date
  subscriptionEnd?: Date
  createdAt: Date
}

// 会话类型
export interface Session {
  user: User
  accessToken: string
  refreshToken: string
  expiresAt: Date
}

// 主题类型
export type Theme = 'light' | 'dark' | 'system'

// 导航菜单项
export interface MenuItem {
  label: string
  href: string
  icon?: string
  children?: MenuItem[]
  roles?: string[]
}

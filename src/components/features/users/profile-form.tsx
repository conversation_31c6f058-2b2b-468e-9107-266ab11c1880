'use client'

// 用户档案编辑表单组件
import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { userProfileSchema, type UserProfileData } from '@/lib/validations/user'
import { toast } from 'sonner'

export function ProfileForm() {
  const [isLoading, setIsLoading] = useState(false)
  const [initialLoading, setInitialLoading] = useState(true)

  const form = useForm<UserProfileData>({
    resolver: zodResolver(userProfileSchema),
    defaultValues: {
      fullName: '',
      email: '',
    },
  })

  // 加载当前用户信息
  useEffect(() => {
    async function loadProfile() {
      try {
        const response = await fetch('/api/users/profile')
        const result = await response.json()

        if (response.ok && result.data) {
          form.reset({
            fullName: result.data.fullName,
            email: result.data.email,
          })
        }
      } catch (error) {
        console.error('Failed to load profile:', error)
      } finally {
        setInitialLoading(false)
      }
    }

    loadProfile()
  }, [form])

  async function onSubmit(data: UserProfileData) {
    setIsLoading(true)
    
    try {
      const response = await fetch('/api/users/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      const result = await response.json()

      if (!response.ok) {
        toast.error(result.error?.message || '更新失败')
        return
      }

      toast.success('个人信息更新成功')
      
      // 如果邮箱发生变化，提示用户重新登录
      if (data.email !== form.formState.defaultValues?.email) {
        toast.info('邮箱已更新，请重新登录以确保安全')
      }
    } catch {
      toast.error('更新失败，请稍后重试')
    } finally {
      setIsLoading(false)
    }
  }

  if (initialLoading) {
    return (
      <div className="space-y-4">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
          <div className="h-10 bg-gray-200 rounded"></div>
        </div>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
          <div className="h-10 bg-gray-200 rounded"></div>
        </div>
        <div className="h-10 bg-gray-200 rounded animate-pulse"></div>
      </div>
    )
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="fullName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>姓名</FormLabel>
              <FormControl>
                <Input
                  placeholder="请输入姓名"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>邮箱</FormLabel>
              <FormControl>
                <Input
                  type="email"
                  placeholder="请输入邮箱"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button
          type="submit"
          className="w-full"
          disabled={isLoading || !form.formState.isDirty}
        >
          {isLoading ? '更新中...' : '更新信息'}
        </Button>
      </form>
    </Form>
  )
}

'use client'

// 用户档案信息组件
import { useState, useEffect } from 'react'
import { Badge } from '@/components/ui/badge'
import { formatDate } from '@/lib/utils/format'

interface UserProfile {
  id: string
  authUserId: string
  email: string
  fullName: string
  role: 'USER' | 'ADMIN' | 'SUPER'
  isActive: boolean
  subscriptionStart: string | null
  subscriptionEnd: string | null
  inviteCodeUsed: string | null
  createdAt: string
  updatedAt: string
}

export function ProfileInfo() {
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchProfile() {
      try {
        const response = await fetch('/api/users/profile')
        const result = await response.json()

        if (!response.ok) {
          setError(result.error?.message || '获取用户信息失败')
          return
        }

        setProfile(result.data)
      } catch {
        setError('网络错误，请稍后重试')
      } finally {
        setLoading(false)
      }
    }

    fetchProfile()
  }, [])

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(6)].map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-1/3 mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-2/3"></div>
          </div>
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-4">
        <p className="text-red-600">{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-2 text-blue-600 hover:underline"
        >
          重试
        </button>
      </div>
    )
  }

  if (!profile) {
    return (
      <div className="text-center py-4">
        <p className="text-gray-500">未找到用户信息</p>
      </div>
    )
  }

  const getRoleBadge = (role: string) => {
    switch (role) {
      case 'SUPER':
        return <Badge variant="destructive">超级管理员</Badge>
      case 'ADMIN':
        return <Badge variant="secondary">管理员</Badge>
      case 'USER':
      default:
        return <Badge variant="outline">普通用户</Badge>
    }
  }

  const getStatusBadge = (isActive: boolean) => {
    return isActive ? (
      <Badge variant="default">激活</Badge>
    ) : (
      <Badge variant="destructive">禁用</Badge>
    )
  }

  const isSubscriptionExpired = profile.subscriptionEnd &&
    new Date(profile.subscriptionEnd) < new Date()

  return (
    <div className="space-y-4">
      <div>
        <label className="text-sm font-medium text-gray-600">姓名</label>
        <p className="text-sm text-gray-900">{profile.fullName}</p>
      </div>

      <div>
        <label className="text-sm font-medium text-gray-600">邮箱</label>
        <p className="text-sm text-gray-900">{profile.email}</p>
      </div>

      <div>
        <label className="text-sm font-medium text-gray-600">角色</label>
        <div className="mt-1">
          {getRoleBadge(profile.role)}
        </div>
      </div>

      <div>
        <label className="text-sm font-medium text-gray-600">账户状态</label>
        <div className="mt-1">
          {getStatusBadge(profile.isActive)}
        </div>
      </div>

      {profile.subscriptionStart && (
        <div>
          <label className="text-sm font-medium text-gray-600">订阅开始</label>
          <p className="text-sm text-gray-900">
            {formatDate(profile.subscriptionStart)}
          </p>
        </div>
      )}

      {profile.subscriptionEnd && (
        <div>
          <label className="text-sm font-medium text-gray-600">订阅到期</label>
          <div className="flex items-center space-x-2">
            <p className="text-sm text-gray-900">
              {formatDate(profile.subscriptionEnd)}
            </p>
            {isSubscriptionExpired && (
              <Badge variant="destructive">已过期</Badge>
            )}
          </div>
        </div>
      )}

      {profile.inviteCodeUsed && (
        <div>
          <label className="text-sm font-medium text-gray-600">使用的邀请码</label>
          <p className="text-sm text-gray-900 font-mono">
            {profile.inviteCodeUsed}
          </p>
        </div>
      )}

      <div>
        <label className="text-sm font-medium text-gray-600">注册时间</label>
        <p className="text-sm text-gray-900">
          {formatDate(profile.createdAt)}
        </p>
      </div>
    </div>
  )
}

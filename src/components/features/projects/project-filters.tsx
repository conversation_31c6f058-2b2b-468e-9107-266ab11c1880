'use client'

// 项目筛选组件
import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

export function ProjectFilters() {
  const [filters, setFilters] = useState({
    keyword: '',
    type: '',
    dateFrom: '',
    dateTo: '',
    hasResources: '',
  })

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const handleSearch = () => {
    // TODO: 实现搜索逻辑
    console.log('搜索条件:', filters)
  }

  const handleReset = () => {
    setFilters({
      keyword: '',
      type: '',
      dateFrom: '',
      dateTo: '',
      hasResources: '',
    })
  }

  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor="keyword">关键词</Label>
        <Input
          id="keyword"
          placeholder="搜索项目标题或编号"
          value={filters.keyword}
          onChange={(e) => handleFilterChange('keyword', e.target.value)}
        />
      </div>

      <div>
        <Label htmlFor="type">项目类型</Label>
        <Select value={filters.type} onValueChange={(value: string) => handleFilterChange('type', value)}>
          <SelectTrigger>
            <SelectValue placeholder="选择项目类型" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">全部类型</SelectItem>
            <SelectItem value="CONTRACT">合同</SelectItem>
            <SelectItem value="GRANT">资助</SelectItem>
            <SelectItem value="OTHER">其他</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label htmlFor="dateFrom">发布日期（从）</Label>
        <Input
          id="dateFrom"
          type="date"
          value={filters.dateFrom}
          onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
        />
      </div>

      <div>
        <Label htmlFor="dateTo">发布日期（到）</Label>
        <Input
          id="dateTo"
          type="date"
          value={filters.dateTo}
          onChange={(e) => handleFilterChange('dateTo', e.target.value)}
        />
      </div>

      <div>
        <Label htmlFor="hasResources">是否有附件</Label>
        <Select value={filters.hasResources} onValueChange={(value: string) => handleFilterChange('hasResources', value)}>
          <SelectTrigger>
            <SelectValue placeholder="选择附件状态" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="">全部</SelectItem>
            <SelectItem value="true">有附件</SelectItem>
            <SelectItem value="false">无附件</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="flex space-x-2">
        <Button onClick={handleSearch} className="flex-1">
          搜索
        </Button>
        <Button variant="outline" onClick={handleReset}>
          重置
        </Button>
      </div>
    </div>
  )
}

'use client'

// 项目列表组件
import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { formatDate } from "@/lib/utils/format"
import { createClient } from '@/lib/supabase/client'
import type { Project } from '@/types/database'

export function ProjectList() {
  const [projects, setProjects] = useState<Project[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const supabase = createClient()

  useEffect(() => {
    async function fetchProjects() {
      try {
        const { data, error } = await supabase
          .from('us_sam_gov_project_view')
          .select('*')
          .order('posted_date', { ascending: false })
          .limit(20)

        if (error) {
          setError('获取项目数据失败')
          return
        }

        setProjects(data || [])
      } catch (err) {
        setError('网络错误，请稍后重试')
      } finally {
        setLoading(false)
      }
    }

    fetchProjects()
  }, [supabase])

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(5)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="animate-pulse space-y-3">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-red-600">{error}</p>
          <Button 
            onClick={() => window.location.reload()} 
            className="mt-4"
          >
            重试
          </Button>
        </CardContent>
      </Card>
    )
  }

  if (projects.length === 0) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-gray-500">暂无项目数据</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      {projects.map((project) => (
        <Card key={project.id} className="hover:shadow-md transition-shadow">
          <CardHeader>
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <CardTitle className="text-lg line-clamp-2">
                  {project.title}
                </CardTitle>
                <CardDescription className="mt-1">
                  项目编号: {project.solicitationnumber}
                </CardDescription>
              </div>
              <Badge variant={project.type === 'CONTRACT' ? 'default' : 'secondary'}>
                {project.type}
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-gray-600">采购机构</p>
                <p className="font-medium line-clamp-2">{project.fullparentpathname}</p>
              </div>
              <div>
                <p className="text-gray-600">发布日期</p>
                <p className="font-medium">{formatDate(project.posted_date)}</p>
              </div>
              {project.poc_fullname && (
                <div>
                  <p className="text-gray-600">联系人</p>
                  <p className="font-medium">{project.poc_fullname}</p>
                </div>
              )}
              {project.awardee_name && (
                <div>
                  <p className="text-gray-600">中标方</p>
                  <p className="font-medium">{project.awardee_name}</p>
                </div>
              )}
            </div>
            <div className="flex items-center justify-between mt-4">
              <div className="flex items-center space-x-2">
                {project.has_resources && (
                  <Badge variant="outline">有附件</Badge>
                )}
              </div>
              <Button variant="outline" size="sm">
                查看详情
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

'use client'

// 注册表单组件
import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { registerSchema, type RegisterFormData } from '@/lib/validations/auth'
import { toast } from 'sonner'
import { Turnstile } from './turnstile'

export function RegisterForm() {
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()

  const form = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      email: '',
      password: '',
      confirmPassword: '',
      fullName: '',
      inviteCode: '',
      turnstileToken: '',
    },
  })

  const handleTurnstileVerify = (token: string) => {
    form.setValue('turnstileToken', token)
  }

  const handleTurnstileError = () => {
    form.setValue('turnstileToken', '')
    toast.error('人机验证失败，请重试')
  }

  async function onSubmit(data: RegisterFormData) {
    if (!data.turnstileToken) {
      toast.error('请完成人机验证')
      return
    }

    setIsLoading(true)

    try {
      // 调用注册 API 而不是直接操作 Supabase
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      const result = await response.json()

      if (!response.ok) {
        toast.error(result.error?.message || '注册失败')
        return
      }

      toast.success('注册成功！请查看邮箱确认账户')
      router.push('/login')
    } catch {
      toast.error('注册失败，请稍后重试')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="inviteCode"
          render={({ field }) => (
            <FormItem>
              <FormLabel>邀请码</FormLabel>
              <FormControl>
                <Input
                  placeholder="请输入邀请码"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="fullName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>姓名</FormLabel>
              <FormControl>
                <Input
                  placeholder="请输入姓名"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>邮箱</FormLabel>
              <FormControl>
                <Input
                  type="email"
                  placeholder="请输入邮箱"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>密码</FormLabel>
              <FormControl>
                <Input
                  type="password"
                  placeholder="请输入密码"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="confirmPassword"
          render={({ field }) => (
            <FormItem>
              <FormLabel>确认密码</FormLabel>
              <FormControl>
                <Input
                  type="password"
                  placeholder="请再次输入密码"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Turnstile 人机验证 */}
        <div className="space-y-2">
          <label className="text-sm font-medium">人机验证</label>
          <Turnstile
            siteKey={process.env.NEXT_PUBLIC_TURNSTILE_SITE_KEY || ''}
            onVerify={handleTurnstileVerify}
            onError={handleTurnstileError}
            onExpire={() => form.setValue('turnstileToken', '')}
            theme="auto"
            className="flex justify-center"
          />
        </div>

        <Button
          type="submit"
          className="w-full"
          disabled={isLoading || !form.watch('turnstileToken')}
        >
          {isLoading ? '注册中...' : '注册'}
        </Button>
      </form>
    </Form>
  )
}

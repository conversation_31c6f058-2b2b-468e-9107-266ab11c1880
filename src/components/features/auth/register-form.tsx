'use client'

// 注册表单组件
import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { registerSchema, type RegisterFormData } from '@/lib/validations/auth'
import { createClient } from '@/lib/supabase/client'
import { toast } from 'sonner'

export function RegisterForm() {
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()
  const supabase = createClient()

  const form = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      email: '',
      password: '',
      confirmPassword: '',
      fullName: '',
      inviteCode: '',
      turnstileToken: '',
    },
  })

  async function onSubmit(data: RegisterFormData) {
    setIsLoading(true)

    try {
      // 首先验证邀请码
      const { data: inviteCodeData, error: inviteError } = await supabase
        .from('us_sam_u_invite_codes')
        .select('*')
        .eq('code', data.inviteCode)
        .eq('is_active', true)
        .single()

      if (inviteError || !inviteCodeData) {
        toast.error('邀请码无效或已过期')
        return
      }

      if (inviteCodeData.current_uses >= inviteCodeData.max_uses) {
        toast.error('邀请码使用次数已达上限')
        return
      }

      if (inviteCodeData.expires_at && new Date(inviteCodeData.expires_at) < new Date()) {
        toast.error('邀请码已过期')
        return
      }

      // 注册用户
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: data.email,
        password: data.password,
        options: {
          data: {
            full_name: data.fullName,
          },
        },
      })

      if (authError) {
        toast.error('注册失败：' + authError.message)
        return
      }

      if (authData.user) {
        // 创建用户档案
        const { error: profileError } = await supabase
          .from('us_sam_u_profiles')
          .insert({
            auth_user_id: authData.user.id,
            email: data.email,
            full_name: data.fullName,
            invite_code_used: data.inviteCode,
            subscription_start: new Date().toISOString(),
            subscription_end: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7天试用
          })

        if (profileError) {
          toast.error('创建用户档案失败')
          return
        }

        // 更新邀请码使用次数
        await supabase
          .from('us_sam_u_invite_codes')
          .update({ current_uses: inviteCodeData.current_uses + 1 })
          .eq('id', inviteCodeData.id)

        toast.success('注册成功！请查看邮箱确认账户')
        router.push('/login')
      }
    } catch {
      toast.error('注册失败，请稍后重试')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="inviteCode"
          render={({ field }) => (
            <FormItem>
              <FormLabel>邀请码</FormLabel>
              <FormControl>
                <Input
                  placeholder="请输入邀请码"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="fullName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>姓名</FormLabel>
              <FormControl>
                <Input
                  placeholder="请输入姓名"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>邮箱</FormLabel>
              <FormControl>
                <Input
                  type="email"
                  placeholder="请输入邮箱"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>密码</FormLabel>
              <FormControl>
                <Input
                  type="password"
                  placeholder="请输入密码"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="confirmPassword"
          render={({ field }) => (
            <FormItem>
              <FormLabel>确认密码</FormLabel>
              <FormControl>
                <Input
                  type="password"
                  placeholder="请再次输入密码"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button
          type="submit"
          className="w-full"
          disabled={isLoading}
        >
          {isLoading ? '注册中...' : '注册'}
        </Button>
      </form>
    </Form>
  )
}

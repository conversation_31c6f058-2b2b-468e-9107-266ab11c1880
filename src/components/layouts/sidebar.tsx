'use client'

// 侧边栏组件
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { 
  FileText, 
  Archive, 
  User, 
  BarChart3,
  Users,
  Ticket,
  FileBarChart
} from 'lucide-react'

const navigation = [
  {
    name: '项目列表',
    href: '/projects',
    icon: FileText,
  },
  {
    name: '历史数据',
    href: '/archived',
    icon: Archive,
  },
  {
    name: '数据统计',
    href: '/stats',
    icon: BarChart3,
  },
  {
    name: '个人中心',
    href: '/profile',
    icon: User,
  },
]

const adminNavigation = [
  {
    name: '用户管理',
    href: '/admin/users',
    icon: Users,
  },
  {
    name: '邀请码管理',
    href: '/admin/invite-codes',
    icon: Ticket,
  },
  {
    name: '系统日志',
    href: '/admin/logs',
    icon: FileBarChart,
  },
]

export function Sidebar() {
  const pathname = usePathname()

  return (
    <div className="w-64 bg-white border-r border-gray-200 min-h-screen">
      <nav className="p-4 space-y-2">
        <div className="space-y-1">
          {navigation.map((item) => {
            const isActive = pathname === item.href
            return (
              <Button
                key={item.name}
                variant={isActive ? "secondary" : "ghost"}
                className={cn(
                  "w-full justify-start",
                  isActive && "bg-blue-50 text-blue-700"
                )}
                asChild
              >
                <Link href={item.href}>
                  <item.icon className="mr-2 h-4 w-4" />
                  {item.name}
                </Link>
              </Button>
            )
          })}
        </div>

        {/* 管理员功能 - 这里应该根据用户权限显示 */}
        <div className="pt-4 border-t border-gray-200">
          <p className="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">
            管理功能
          </p>
          <div className="space-y-1">
            {adminNavigation.map((item) => {
              const isActive = pathname === item.href
              return (
                <Button
                  key={item.name}
                  variant={isActive ? "secondary" : "ghost"}
                  className={cn(
                    "w-full justify-start",
                    isActive && "bg-blue-50 text-blue-700"
                  )}
                  asChild
                >
                  <Link href={item.href}>
                    <item.icon className="mr-2 h-4 w-4" />
                    {item.name}
                  </Link>
                </Button>
              )
            })}
          </div>
        </div>
      </nav>
    </div>
  )
}

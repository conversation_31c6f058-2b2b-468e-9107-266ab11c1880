# 技术设计文档（TDD）

## 目录
1. 项目简介
2. 技术栈与依赖
3. 目录结构说明
4. 认证与注册流程设计
5. 数据库表结构说明
6. 首页与认证注册页面设计
7. 数据浏览与展示模块设计
8. 暗色模式实现方案
9. PWA 设计（可选）
10. Supabase 数据安全与RLS策略
11. 部署与环境变量管理
12. 代码规范与开发流程
13. 变更记录

---

## 1. 项目简介
本项目为极简数据展示平台，基于Next.js 15、Tailwind CSS、shadcn/ui、Supabase等现代技术栈，支持邀请注册、登录、首页图表与数据浏览，前端全英文，开发注释全中文，支持暗色模式，部署于Vercel。

## 2. 技术栈与依赖
详见docs/prd.md，已安装并初始化Next.js 15、Tailwind CSS、shadcn/ui、Lucide Icons、Recharts、React Hook Form、Zod、Supabase-js、Prettier等。

## 3. 目录结构说明
- docs/：项目文档，包括prd.md、TDD.md、chat.log、changelog等
- src/app/：Next.js App Router主目录，包含全局样式、布局、页面等
- src/lib/：工具函数目录（shadcn/ui自动生成）
- public/：静态资源目录
- 其他配置文件：如tailwind.config、next.config、tsconfig等

## 4. 认证与注册流程设计

### 4.1 流程概述
- 首页为登录窗口，未登录用户自动跳转至登录页。
- 注册采用邀请制，用户需通过邀请码注册。
- 注册/登录表单均集成Cloudflare Turnstile防护。
- 用户注册成功后自动登录，进入数据展示首页。
- 登录状态持久化，未登录访问受限页面自动跳转登录。

### 4.2 邀请码机制
- Supabase 数据库新建 `invites` 表，字段包括：邀请码（code）、状态（used/unused）、被邀请邮箱（email，可选）、创建时间等。
- 注册时需填写有效的邀请码，后端校验邀请码有效性与唯一性。
- 注册成功后邀请码状态设为已用。

### 4.3 Turnstile 集成
- 前端表单集成 Turnstile 组件，使用 `NEXT_PUBLIC_TURNSTILE_SITE_KEY`。
- 后端（Supabase Edge Function 或 Vercel API Route）校验 Turnstile token，使用 `TURNSTILE_SECRET_KEY`。
- secret key 仅在后端环境变量中设置，绝不暴露前端。
- Supabase Edge Function 通过 Supabase 控制台环境变量读取，Vercel API Route 通过 Vercel 环境变量读取。

### 4.4 环境变量安全说明
- `NEXT_PUBLIC_TURNSTILE_SITE_KEY` 仅用于前端，公开。
- `TURNSTILE_SECRET_KEY` 仅用于后端，分别在 Supabase Edge Function 或 Vercel API Route 环境变量中设置。
- 本地开发可写入 `.env.local`，生产环境分别在 Supabase/Vercel 后台设置。

### 4.5 前后端分工
- 前端：表单UI、Turnstile集成、表单校验、调用注册/登录API。
- 后端：校验Turnstile、校验邀请码、注册用户、更新邀请码状态、返回认证token。

### 4.6 流程图描述
1. 用户访问首页，未登录则显示登录窗口。
2. 用户点击注册，填写邮箱、密码、邀请码、通过Turnstile验证。
3. 前端提交注册请求，后端依次校验Turnstile、邀请码、注册用户、更新邀请码。
4. 注册成功后自动登录，跳转首页。
5. 登录流程同理，增加Turnstile校验。

2025-05-25 11:05
- 补充认证与注册流程详细设计，明确邀请码、Turnstile集成与环境变量安全方案。

## 5. 数据库表结构说明

### 5.1 用户扩展信息表 us_sam_u_profiles
- 关联 Supabase auth.users，存储用户业务扩展信息。
- 字段涵盖邮箱、角色、邀请码、邀请人、订阅信息、账户状态、支付信息、扩展元数据等。
- 启用 RLS（行级安全），只允许本人或管理员查询/更新，保障数据安全。
- 适合多租户、SaaS 场景扩展。

### 5.2 邀请码表 us_sam_u_invite_codes
- 存储邀请码、使用状态、邀请人、使用者、使用次数、过期时间等。
- 支持邀请码多次使用（usage_limit/usage_count），可扩展批量邀请、限时邀请等场景。
- 通过触发器自动更新时间戳，便于审计和追踪。

### 5.3 设计亮点与安全性
- RLS 控制所有敏感数据访问，极大提升安全性。
- 邀请码机制灵活，便于后续运营和权限管理。
- profile 表预留丰富扩展字段，支持业务快速迭代。
- 所有表均有详细注释，便于团队协作和代码审计。

2025-05-25 11:05
- 补充数据库表结构说明，明确us_sam_u_profiles和us_sam_u_invite_codes的设计要点与安全性。

## 6. 首页与认证注册页面设计

### 6.1 页面结构
- `/`（首页）：未登录用户自动显示登录窗口，已登录用户跳转数据展示首页。
- `/login`：登录页面，表单字段：Email、Password、Turnstile 验证。
- `/register`：注册页面，表单字段：Email、Password、Invite Code、Turnstile 验证。
- 所有页面均为英文界面，所有开发注释为中文。

### 6.2 交互流程
- 未登录用户访问任何受限页面自动跳转 `/login`。
- 登录表单校验通过后，调用 Supabase Auth 登录，成功后跳转首页。
- 注册表单校验通过后，先校验 Turnstile，再校验邀请码，最后调用 Supabase Auth 注册，成功后自动登录并跳转首页。
- 表单交互采用 shadcn/ui 组件，表单管理用 React Hook Form，校验用 Zod。
- 所有错误提示、按钮、表单标签均为英文。

### 6.3 Turnstile 集成
- 登录与注册表单底部集成 Turnstile 验证组件。
- 前端用 `NEXT_PUBLIC_TURNSTILE_SITE_KEY` 渲染，提交时将 token 一并上传后端校验。

### 6.4 代码规范
- 所有页面、组件、hooks、API 调用均用英文命名。
- 业务逻辑、表单校验、API 交互等关键代码处均用中文注释，便于团队协作。

2025-05-25 11:05
- 补充首页与认证注册相关页面的UI与交互设计，明确英文界面与中文注释规范。

## 7. 数据浏览与展示模块设计

## 8. 暗色模式实现方案
- Tailwind CSS和shadcn/ui已在globals.css中配置CSS变量，支持暗色模式。
- 通过在html/body或根节点添加.dark类即可切换暗色模式。
- 当前layout.tsx未实现自动切换逻辑，后续可通过JS或系统偏好实现。

2025-05-25 11:05
- 完成shadcn/ui初始化，完善TDD文档结构及部分内容。

## 注册 API 优化（2024-06-09）
- 支持邀请码多次使用：校验 usage_limit（使用上限）、usage_count（已用次数），仅在达到上限时设置 is_used: true。
- 校验邀请码过期时间：expires_at 字段，过期不可用。
- 更新逻辑：每次注册成功后 usage_count+1，若达到 usage_limit 则 is_used: true 且记录 used_by，否则只自增 usage_count。
- 失败场景：邀请码不存在、已用尽、已过期、Supabase 写入失败、Turnstile 校验失败。
- 相关表结构见 docs/sql/us_sam_u_invite_codes.sql。

## 登录 API 实现（2024-06-09）
- 接口路径：/api/auth/login，POST 方法。
- 参数：email、password、turnstile（Cloudflare token）。
- 处理流程：
  1. 校验 Turnstile token（后端 secret key）。
  2. 调用 Supabase Auth 登录。
  3. 登录成功返回 session、user 信息。
  4. 登录失败返回明确错误。
- 异常处理：Turnstile 校验失败、Supabase 登录失败、其他异常。
- 返回结构与注册 API 风格一致，便于前端统一处理。

## 首页弹窗登录/注册体验重构（2024-06-09）
- 登录/注册表单均提取为独立组件，支持页面和弹窗复用。
- Header 只保留 Login 按钮，点击后弹窗主视觉区域。
- 注册表单整合进登录表单下方，点击 Sign up 展开注册表单。
- 弹窗支持点击遮罩关闭，右上角 × 关闭。
- 弹窗和表单容器优化响应式，移动端体验良好。
- 代码结构高内聚低耦合，便于维护和扩展。

## Auth API切换为service key与安全性最佳实践（2024-06-09）
- 所有后端API（注册、登录、邀请码等）均用SUPABASE_SERVICE_ROLE_KEY，拥有全部表权限。
- service key仅在后端环境变量中配置，绝不暴露前端。
- RLS只保护前端直连，后端API可绕过RLS，权限逻辑在后端实现。
- 登录/注册API用service key是安全的最佳实践。

- 注册表单修正：注册成功后不再跳转，页面停留并提示用户检查邮箱激活账号，体验符合现代产品标准。 
# 项目技术选型与说明

## 技术栈总览

| 分类         | 技术选型         | 说明                                                         |
| ------------ | ---------------- | ------------------------------------------------------------ |
| 前端框架     | Next.js 15       | App Router，支持 SSR/SSG，默认 TypeScript                    |
| CSS 框架     | Tailwind CSS     | 原子化 CSS，支持暗色模式                                     |
| UI 组件库    | shadcn/ui        | 现代 React 组件，易自定义，支持暗色模式                      |
| 图标库       | Lucide Icons     | 现代 SVG 图标库，风格统一                                    |
| 图表库       | Recharts         | React 图表库，API 简单，适合数据展示                         |
| 表单处理     | React Hook Form  | 轻量表单状态管理，性能优异                                   |
| 表单校验     | Zod              | TypeScript 优先的数据校验库                                  |
| 状态管理     | React 内置/Context| 极简项目无需额外状态库                                       |
| 数据库       | Supabase         | 开源 BaaS，PostgreSQL，RLS 安全                              |
| 环境变量     | .env.local       | Next.js 推荐方式，安全管理密钥                               |
| 代码质量     | TypeScript、ESLint、Prettier | 类型安全、风格统一                                 |
| 暗色模式     | Tailwind CSS、shadcn/ui | 原生支持，易于实现                                   |
| PWA（可选）  | next-pwa         | 让网页可安装、离线访问，提升移动端体验（可后续集成）         |
| 部署         | Vercel           | Next.js 官方推荐，自动化部署                                 |
| 包管理       | pnpm             | 高效、快速、节省空间的包管理工具，推荐用于现代前端项目       |

---

## 技术选型详细说明

- **Next.js 15 (App Router)**：现代 React 全栈框架，支持服务端渲染、静态生成、API 路由等，App Router 提供更灵活的页面和布局管理。
- **Tailwind CSS**：原子化 CSS 框架，极简、灵活，适合快速开发和自定义样式，易于实现响应式和暗色模式。
- **shadcn/ui**：基于 Radix UI 和 Tailwind CSS 的现代 React 组件库，组件可自定义、无障碍支持好，适合极简风格。
- **Lucide Icons**：现代、开源的 SVG 图标库，风格统一，易于集成到 shadcn/ui。
- **Recharts**：用于首页和数据展示的简单图表，基于 React，API 简单，适合快速开发。
- **React Hook Form + Zod**：轻量级表单状态管理库与 TypeScript 优先的数据校验库，和 shadcn/ui 配合良好。
- **Supabase**：开源后端即服务（BaaS），兼容 PostgreSQL，支持实时、认证、存储等，已启用 RLS（行级安全），保证数据安全。
- **.env.local**：用于安全管理 Supabase/Vercel 的密钥。
- **TypeScript、ESLint、Prettier**：提升类型安全和开发效率，保证代码风格统一和质量。
- **pnpm**：高效、快速、节省空间的包管理工具，推荐用于现代前端项目。
- **暗色模式（Dark Mode）**：Tailwind CSS 原生支持暗色模式，shadcn/ui 组件也支持，可通过系统偏好或手动切换实现。
- **PWA（Progressive Web App）**：让网页像原生 App 一样可安装到桌面/手机，支持离线访问、推送通知等。Next.js 可通过插件（如 next-pwa）实现，适合提升移动端体验，初期可不强制。
- **Vercel**：Next.js 官方推荐部署平台，自动化部署，支持自定义域名和环境变量。

---

## 术语解释

- **暗色模式（Dark Mode）**：界面以深色为主，减少眼睛疲劳，适合夜间或低光环境。Tailwind CSS 和 shadcn/ui 都支持暗色模式切换。
- **PWA（Progressive Web App）**：让网页像原生 App 一样可安装到桌面/手机，支持离线访问、推送通知等。适合提升用户体验，但不是所有项目都必须。
- **pnpm**：现代高效的包管理工具，安装速度快、磁盘占用小，适合大型和现代前端项目。

## 2025-05-25 20:06 主界面与侧边栏交互体验优化

1. 侧边栏菜单项icon尺寸统一，ICON_SIZE常量集中管理，所有icon样式全局同步。
2. 菜单项icon与文字始终左对齐，pl-4/gap-4，hover展开/收起时icon和logo位置不跳动。
3. logo左对齐且不移动，视觉风格统一。
4. aside右侧新增after伪元素，hover时自动加box-shadow，增强抽屉式拉开感。
5. 侧边栏hover展开动画时长加长（duration-1000），动画更顺滑自然。
6. 用户信息DropdownMenu弹出时，自动禁用侧边栏hover展开/收起，避免误收起。
7. 权限与主界面逻辑优化，未登录用户无法访问主界面，登录后体验流畅。
8. 代码结构优化，所有菜单项布局、icon、动画、分组、Dropdown等均按最佳实践实现，便于维护和扩展。

**涉及文件：**
- src/components/app-sidebar.tsx
- src/components/app-sidebar.css
- 侧边栏相关依赖与全局样式

**备注：**
本次优化极大提升了侧边栏的交互体验、视觉一致性和代码可维护性，所有细节均可通过常量和样式集中调整。 
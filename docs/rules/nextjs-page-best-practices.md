# Next.js Page.tsx 文件最佳实践指南

## 行数建议

| 文件行数 | 复杂度等级 | 建议 |
|---------|-----------|------|
| **≤ 50 行** | 简单 | ✅ 理想状态，易于维护 |
| **50-100 行** | 中等 | ✅ 可接受，注意组件抽取 |
| **100-150 行** | 复杂 | ⚠️ 需要重构规划 |
| **> 150 行** | 过于复杂 | ❌ 必须拆分重构 |

## 核心原则

### Page.tsx 应该专注于：
1. **路由层面的数据获取** - Server Components 数据预取
2. **页面布局组合** - 组织主要的页面结构
3. **SEO 和元数据** - 页面标题、描述等
4. **参数处理** - URL 参数和搜索参数
5. **错误边界** - 页面级别的错误处理

### Page.tsx 应该避免：
1. **复杂的业务逻辑** - 应抽取到服务层或自定义钩子
2. **详细的 UI 实现** - 应拆分为独立组件
3. **大量的事件处理** - 应委托给子组件
4. **复杂的状态管理** - 使用状态管理库或上下文
5. **重复代码** - 抽取为可复用组件或工具函数

## 不同复杂度的示例

### 简单页面 (≤ 50 行) ✅

```tsx
// app/about/page.tsx
import { Metadata } from 'next';
import { Hero, Team, Values } from '@/components/about';

export const metadata: Metadata = {
  title: '关于我们',
  description: '了解我们的团队和价值观',
};

export default function AboutPage() {
  return (
    <div className="min-h-screen">
      <Hero />
      <Values />
      <Team />
    </div>
  );
}
```

### 中等复杂页面 (50-100 行) ✅

```tsx
// app/blog/page.tsx
import { Metadata } from 'next';
import { Suspense } from 'react';
import { BlogList, BlogSidebar, BlogHeader } from '@/components/blog';
import { getBlogPosts, getBlogCategories } from '@/lib/blog';

export const metadata: Metadata = {
  title: '博客 - 最新文章和思考',
  description: '阅读我们的最新技术文章和行业见解',
};

interface BlogPageProps {
  searchParams: {
    category?: string;
    page?: string;
  };
}

export default async function BlogPage({ searchParams }: BlogPageProps) {
  const category = searchParams.category;
  const page = parseInt(searchParams.page || '1', 10);
  
  // 并行获取数据
  const [posts, categories] = await Promise.all([
    getBlogPosts({ category, page, limit: 10 }),
    getBlogCategories(),
  ]);

  return (
    <div className="container mx-auto px-4 py-8">
      <BlogHeader />
      
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <main className="lg:col-span-3">
          <Suspense fallback={<div>加载中...</div>}>
            <BlogList posts={posts} currentPage={page} />
          </Suspense>
        </main>
        
        <aside className="lg:col-span-1">
          <BlogSidebar categories={categories} currentCategory={category} />
        </aside>
      </div>
    </div>
  );
}
```

### 需要重构的页面 (> 150 行) ❌

```tsx
// 这是一个需要重构的例子 - 太多职责集中在一个文件中
export default function BadDashboardPage() {
  // ❌ 太多状态定义
  const [projects, setProjects] = useState([]);
  const [teams, setTeams] = useState([]);
  const [notifications, setNotifications] = useState([]);
  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // ❌ 太多副作用
  useEffect(() => {
    // 大量的数据获取逻辑...
  }, []);
  
  // ❌ 太多事件处理函数
  const handleProjectCreate = () => { /* ... */ };
  const handleTeamInvite = () => { /* ... */ };
  const handleNotificationRead = () => { /* ... */ };
  
  // ❌ 复杂的条件渲染逻辑
  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;
  
  return (
    <div>
      {/* 大量的 JSX 代码... */}
    </div>
  );
}
```

## 重构策略

### 1. 组件抽取

将复杂的页面拆分为多个专门的组件：

```tsx
// app/dashboard/page.tsx - 重构后 ✅
import { Metadata } from 'next';
import { 
  DashboardHeader, 
  ProjectsSection, 
  TeamsSection, 
  AnalyticsSection,
  NotificationsPanel 
} from '@/components/dashboard';
import { getDashboardData } from '@/lib/dashboard';

export const metadata: Metadata = {
  title: '仪表盘',
  description: '查看项目概览和团队动态',
};

export default async function DashboardPage() {
  const data = await getDashboardData();
  
  return (
    <div className="dashboard-layout">
      <DashboardHeader />
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <ProjectsSection projects={data.projects} />
          <TeamsSection teams={data.teams} />
        </div>
        
        <div className="space-y-6">
          <AnalyticsSection analytics={data.analytics} />
          <NotificationsPanel notifications={data.notifications} />
        </div>
      </div>
    </div>
  );
}
```

### 2. 自定义钩子抽取

将复杂的逻辑抽取为自定义钩子：

```tsx
// hooks/useDashboard.ts
export function useDashboard() {
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(true);
  
  // 复杂的逻辑实现...
  
  return {
    projects,
    loading,
    refreshProjects,
    createProject,
  };
}

// app/dashboard/page.tsx - 使用自定义钩子
'use client';

export default function DashboardPage() {
  const { projects, loading, refreshProjects } = useDashboard();
  
  if (loading) return <DashboardSkeleton />;
  
  return <DashboardContent projects={projects} onRefresh={refreshProjects} />;
}
```

### 3. 服务层抽取

将数据获取逻辑抽取为服务函数：

```tsx
// lib/services/dashboard.ts
export async function getDashboardData() {
  const [projects, teams, analytics] = await Promise.all([
    getProjects(),
    getTeams(),
    getAnalytics(),
  ]);
  
  return { projects, teams, analytics };
}
```

## 文件组织最佳实践

### 目录结构示例：

```
app/
├── dashboard/
│   ├── _components/           # 页面专用组件
│   │   ├── DashboardHeader.tsx
│   │   ├── ProjectsSection.tsx
│   │   └── index.ts
│   ├── _hooks/               # 页面专用钩子
│   │   └── useDashboard.ts
│   └── page.tsx              # 保持简洁的页面入口
```

### 组件导出管理：

```tsx
// app/dashboard/_components/index.ts
export { DashboardHeader } from './DashboardHeader';
export { ProjectsSection } from './ProjectsSection';
export { TeamsSection } from './TeamsSection';

// app/dashboard/page.tsx - 清洁的导入
import { DashboardHeader, ProjectsSection, TeamsSection } from './_components';
```

## 性能考虑

### 1. 服务器组件优先

```tsx
// 优先使用服务器组件进行数据获取
export default async function BlogPage() {
  const posts = await getBlogPosts(); // 在服务器端获取
  
  return <BlogList posts={posts} />;
}
```

### 2. 客户端组件按需使用

```tsx
// 只在需要交互时使用客户端组件
export default function BlogPage() {
  return (
    <div>
      <StaticContent />          {/* 服务器组件 */}
      <InteractiveWidget />      {/* 客户端组件 */}
    </div>
  );
}
```

### 3. 懒加载重组件

```tsx
import dynamic from 'next/dynamic';

const HeavyChart = dynamic(() => import('@/components/HeavyChart'), {
  loading: () => <ChartSkeleton />,
});

export default function AnalyticsPage() {
  return (
    <div>
      <HeaderStats />
      <HeavyChart />
    </div>
  );
}
```

## 检查清单

### ✅ 页面文件健康检查

- [ ] 文件行数控制在 150 行以内
- [ ] 主要功能已抽取为独立组件
- [ ] 复杂逻辑已抽取为自定义钩子或服务
- [ ] 数据获取逻辑清晰明了
- [ ] SEO 和元数据配置完整
- [ ] 错误处理和加载状态完善
- [ ] 没有重复代码
- [ ] 组件职责单一明确

### ✅ 重构信号

如果页面出现以下情况，需要考虑重构：

- [ ] 超过 150 行代码
- [ ] 包含超过 5 个 useState
- [ ] 包含超过 3 个 useEffect
- [ ] 事件处理函数超过 5 个
- [ ] JSX 嵌套层级过深（> 6 层）
- [ ] 代码审查时需要滚动多次才能看完
- [ ] 新功能难以添加
- [ ] 测试困难

## 总结

保持 `page.tsx` 文件简洁的核心在于**职责单一**和**适度抽象**。页面文件应该像一个**导演**，协调各个组件的工作，而不是**演员**，亲自实现所有细节。

通过遵循这些最佳实践，您的 Next.js 应用将更加易于维护、测试和扩展。

# Supabase 数据访问安全最佳实践

> 更新时间：2024-06-09

---

## 1. RLS + anon key

### 适用场景
- 普通用户日常数据访问（如：个人资料、个人订单、公开数据等）。
- 每个用户只能访问/修改自己的数据。

### 优点
- 前端直连 Supabase，开发简单，体验快。
- RLS（行级安全）保证数据隔离，权限单一，风险可控。

### 缺点
- 只能做"用户只能看/改自己的"场景。
- RLS 规则必须写得非常严谨，否则有越权风险。
- 不适合需要"管理员越权"或批量操作的场景。

---

## 2. service key + 后端 API

### 适用场景
- 管理员后台、系统管理、批量操作、导出、统计等。
- 需要"越权"访问所有用户数据，或无视 RLS 的系统级操作。

### 优点
- service key 拥有超级权限，可绕过所有 RLS。
- 权限判断、日志、风控可集中在后端，安全边界清晰。
- 适合复杂业务逻辑、批量处理、敏感操作。

### 缺点
- 只能在受信任的后端环境使用，**绝不能暴露在前端**。
- 需要额外开发后端 API，前端通过 API 访问数据。

---

## 3. 管理功能为什么推荐后端 API + service key？

1. 管理员经常需要"越权"操作（如查所有用户、批量生成邀请码等），RLS 很难既安全又灵活地支持。
2. service key 可无视 RLS，适合系统级、批量、敏感操作。
3. 后端 API 可集中做权限判断、日志、风控，安全边界更牢靠。
4. 即使前端被攻击，攻击者也无法拿到 service key，无法越权操作。

---

## 4. 场景对比表格

| 场景         | 推荐方式                  | 说明                                   |
|--------------|--------------------------|----------------------------------------|
| 普通用户数据 | 前端 anon key + RLS      | 体验快，RLS隔离，权限单一，风险可控     |
| 管理员功能   | 后端 API + service key   | 权限复杂，需越权，安全边界清晰，易加固  |

---

## 5. 总结

- RLS + anon key 适合"每个人只能看自己的"场景。
- 管理功能（能看/改所有人数据）必须用后端API+service key，安全边界更牢靠。
- **永远不要在前端暴露 service key！**

如需具体权限判断、RLS写法或API设计建议，建议团队内部统一规范，定期安全审计。

---

## 6. 后端管理API接口说明

所有接口均需基于JWT+service key+role三重鉴权：

### 最新推荐方案
- **后端API**：用 service key 连接 Supabase，所有数据操作无视RLS。
- **用户身份校验**：API Route 通过 Authorization 头获取JWT（access_token），用 anon key 校验JWT，获取user id。
- **权限判断**：用 service key 查询 us_sam_u_profiles 表，判断该 user 的 role 字段，只有 role=admin 才允许操作。
- **前端**：登录后自动获取JWT，所有管理API请求自动带上 Authorization: Bearer <access_token>。
- **注册/登录API**：仍用anon key+RLS，普通用户数据隔离，安全高效。

### 典型调用链路
1. 前端用 Supabase Auth 登录，获取 session。
2. 前端调用管理API时，adminApi 自动加上 Authorization: Bearer <access_token>。
3. 后端API用 anon key 校验JWT，获取user id。
4. 后端API用 service key 查us_sam_u_profiles，判断role。
5. 只有admin用户才能操作管理API，所有数据操作均无视RLS。

### 代码片段示例
```typescript
// 前端 adminApi.ts
import { supabase } from '@/lib/supabaseClient';
export async function adminApi<T = any>(url: string, options: RequestInit = {}): Promise<T> {
  const session = await supabase.auth.getSession();
  const token = session.data.session?.access_token;
  if (!token) throw new Error('未登录或token丢失');
  const res = await fetch(url, {
    ...options,
    headers: {
      ...(options.headers || {}),
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
    credentials: 'include',
  });
  const data = await res.json();
  if (!res.ok) throw new Error(data.error || 'API Error');
  return data;
}

// 后端API Route
const supabaseAdmin = createClient(...service key...);
const supabaseAnon = createClient(...anon key...);
async function getUserIdFromJWT(req: NextRequest) { /* ... */ }
async function isAdmin(userId: string) { /* ... */ }
// handler里先校验isAdmin(userId)，再用supabaseAdmin操作数据
```

---

**本项目已全面采用此方案，注册/登录API保持anon key+RLS，管理API全部用service key+JWT+role鉴权。**

### 用户管理
- **GET /api/sysop/users**
  - 查询所有用户，支持分页、邮箱模糊搜索。
  - 参数：`search`（可选，邮箱模糊查找），`page`，`pageSize`
  - 返回：`{ data: UserProfile[], total: number }`
- **PATCH /api/sysop/users**
  - 更新用户信息（昵称、角色、状态、订阅时间、停用/恢复等）。
  - body: `{ id, ...fields }`，如`{ id, full_name, role, status, is_active, subscription_start, subscription_end }`
- **DELETE /api/sysop/users?id=xxx**
  - 删除指定用户。
  - 参数：`id`（用户auth_user_id）

- **POST /api/sysop/users/password**
  - 管理员重置用户密码。
  - body: `{ id, new_password }`

### 邀请码管理
- **GET /api/sysop/invites**
  - 查询所有邀请码，支持分页、code模糊搜索。
  - 参数：`search`（可选，code模糊查找），`page`，`pageSize`
  - 返回：`{ data: InviteCode[], total: number }`
- **POST /api/sysop/invites**
  - 创建邀请码。
  - body: `{ code, usage_limit, expires_at, created_by }`
- **PATCH /api/sysop/invites**
  - 更新邀请码（如作废、修改上限等）。
  - body: `{ id, ...fields }`
- **DELETE /api/sysop/invites?id=xxx**
  - 删除指定邀请码。
  - 参数：`id`（邀请码id）

**所有接口均需后端校验管理员权限，service key仅在后端使用，严禁前端直连。** 
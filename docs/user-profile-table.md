# us_sam_u_profiles 用户扩展表设计

本表为平台用户扩展表，配合 Supabase Auth 使用，支持 SaaS 订阅、角色管理、邀请码注册等。

## 字段设计
| 字段名                  | 类型      | 说明                                               |
|-------------------------|-----------|----------------------------------------------------|
| id                      | uuid      | 主键                                               |
| auth_user_id            | uuid      | 关联 supabase.auth.users.id，唯一                  |
| email                   | text      | 邮箱，便于查询和冗余                               |
| full_name               | text      | 用户姓名                                           |
| role                    | text      | 角色（ADMIN/USER/SUPER/扩展）                      |
| invite_code             | text      | 邀请码，支持邀请码注册                             |
| invited_by              | uuid      | 邀请人ID，便于追踪邀请关系                         |
| subscription_start      | timestamp | 订阅开始时间                                       |
| subscription_end        | timestamp | 订阅到期时间                                       |
| is_active               | boolean   | 是否激活                                           |
| created_at              | timestamp | 创建时间                                           |
| updated_at              | timestamp | 更新时间                                           |
| last_login              | timestamp | 最近登录时间                                       |
| plan                    | text      | 订阅套餐（basic/pro/enterprise等）                 |
| status                  | text      | 账户状态（active/expired/suspended等）             |
| trial_end               | timestamp | 试用期结束时间                                     |
| payment_provider        | text      | 支付渠道（stripe/paypal等）                        |
| payment_customer_id     | text      | 支付平台客户ID                                     |
| payment_subscription_id | text      | 支付平台订阅ID                                     |
| metadata                | jsonb     | 预留扩展字段                                       |

## RLS 策略（行级安全）
- 启用 RLS：`ALTER TABLE us_sam_u_profiles ENABLE ROW LEVEL SECURITY;`
- 只允许本人或管理员查询/更新自己的 profile：
```sql
CREATE POLICY "Users can view their own profile" ON us_sam_u_profiles
  FOR SELECT USING (auth.uid() = auth_user_id OR role = 'ADMIN');
CREATE POLICY "Users can update their own profile" ON us_sam_u_profiles
  FOR UPDATE USING (auth.uid() = auth_user_id OR role = 'ADMIN');
```
- 支持邀请码注册，可结合 API 逻辑和 invite_code 字段实现

## SaaS 平台扩展建议
- 角色支持扩展，role 字段为 text
- 订阅/试用/支付相关字段齐全，便于对接 Stripe/PayPal 等
- metadata 字段为 JSONB，方便后续无损扩展 
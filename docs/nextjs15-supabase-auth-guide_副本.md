# Next.js 15 + Supabase 认证最佳实践方案

## 🎯 明确答案

**强烈推荐使用 SSR 方案 + @supabase/ssr 包**

❌ **不要使用** `@supabase/auth-helpers-*` - 已被官方弃用  
✅ **推荐使用** `@supabase/ssr` - 专为 Next.js SSR 设计的新方案

## 📊 技术栈对比

| 方案 | 状态 | Next.js 15 兼容性 | Vercel 部署 | 稳定性 |
|------|------|-------------------|------------|--------|
| `@supabase/auth-helpers-nextjs` | ❌ 已弃用 | 不兼容 | 有问题 | 不推荐 |
| `@supabase/ssr` | ✅ 推荐 | 完全支持 | 完美支持 | 生产就绪 |

---

## 🚀 推荐方案：@supabase/ssr

### 官方状态确认

<cite>@supabase/auth-helpers 包已被 @supabase/ssr 包取代。我们推荐使用 @supabase/ssr 为您的 Next.js 应用设置认证</cite>。

<cite>@supabase/ssr 包专门为了更好地与 Next.js 框架集成而创建，具有快速配置 Supabase 项目以使用 cookies 存储用户会话的所有功能</cite>。

### 安装和基础配置

```bash
# 安装必要的包
npm install @supabase/supabase-js @supabase/ssr
```

### 创建 Supabase 客户端

#### 1. 环境变量配置

```bash
# .env.local
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

#### 2. 客户端配置文件

```typescript
// utils/supabase/client.ts - 客户端组件使用
import { createBrowserClient } from '@supabase/ssr'

export function createClient() {
  return createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )
}
```

```typescript
// utils/supabase/server.ts - 服务器组件使用
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

export async function createClient() {
  const cookieStore = await cookies()

  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll()
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            )
          } catch {
            // 在 Server Component 中设置 cookies 可能会失败
            // 这是预期行为，中间件会处理这个问题
          }
        },
      },
    }
  )
}
```

#### 3. 中间件配置

```typescript
// middleware.ts
import { createServerClient } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'

export async function middleware(request: NextRequest) {
  let supabaseResponse = NextResponse.next({
    request,
  })

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll()
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => request.cookies.set(name, value))
          supabaseResponse = NextResponse.next({
            request,
          })
          cookiesToSet.forEach(({ name, value, options }) =>
            supabaseResponse.cookies.set(name, value, options)
          )
        },
      },
    }
  )

  // 刷新会话，确保用户登录状态最新
  const {
    data: { user },
  } = await supabase.auth.getUser()

  // 保护路由示例
  if (request.nextUrl.pathname.startsWith('/dashboard') && !user) {
    const url = request.nextUrl.clone()
    url.pathname = '/login'
    return NextResponse.redirect(url)
  }

  return supabaseResponse
}

export const config = {
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}
```

---

## 🔧 完整的用户认证实现

### 1. 数据库扩展表设计

```sql
-- 自定义用户扩展表
CREATE TABLE public.user_profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  username TEXT UNIQUE,
  full_name TEXT,
  avatar_url TEXT,
  bio TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- 启用 RLS
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;

-- 创建策略：用户只能访问自己的数据
CREATE POLICY "Users can view own profile" ON public.user_profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.user_profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.user_profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

-- 自动创建用户档案的触发器
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  INSERT INTO public.user_profiles (id, full_name)
  VALUES (new.id, new.raw_user_meta_data->>'full_name');
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();
```

### 2. TypeScript 类型定义

```typescript
// types/database.ts
export interface UserProfile {
  id: string
  username?: string
  full_name?: string
  avatar_url?: string
  bio?: string
  created_at: string
  updated_at: string
}

export interface Database {
  public: {
    Tables: {
      user_profiles: {
        Row: UserProfile
        Insert: Omit<UserProfile, 'created_at' | 'updated_at'>
        Update: Partial<Omit<UserProfile, 'id' | 'created_at' | 'updated_at'>>
      }
    }
  }
}
```

### 3. 服务器组件中的认证

```tsx
// app/dashboard/page.tsx
import { createClient } from '@/utils/supabase/server'
import { redirect } from 'next/navigation'

export default async function DashboardPage() {
  const supabase = await createClient()
  
  // 获取当前用户
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser()

  if (authError || !user) {
    redirect('/login')
  }

  // 获取用户扩展资料
  const { data: profile, error: profileError } = await supabase
    .from('user_profiles')
    .select('*')
    .eq('id', user.id)
    .single()

  if (profileError) {
    console.error('Error fetching profile:', profileError)
  }

  return (
    <div>
      <h1>欢迎回来，{profile?.full_name || user.email}!</h1>
      <UserProfile profile={profile} />
    </div>
  )
}
```

### 4. 客户端组件中的认证

```tsx
// components/auth/LoginForm.tsx
'use client'

import { useState } from 'react'
import { createClient } from '@/utils/supabase/client'
import { useRouter } from 'next/navigation'

export default function LoginForm() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const router = useRouter()
  const supabase = createClient()

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })

    if (error) {
      alert(error.message)
    } else {
      router.push('/dashboard')
      router.refresh()
    }

    setLoading(false)
  }

  return (
    <form onSubmit={handleLogin} className="space-y-4">
      <div>
        <label htmlFor="email">邮箱</label>
        <input
          id="email"
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
        />
      </div>
      
      <div>
        <label htmlFor="password">密码</label>
        <input
          id="password"
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          required
        />
      </div>
      
      <button type="submit" disabled={loading}>
        {loading ? '登录中...' : '登录'}
      </button>
    </form>
  )
}
```

### 5. Server Actions 用于数据更新

```tsx
// app/profile/actions.ts
'use server'

import { createClient } from '@/utils/supabase/server'
import { revalidatePath } from 'next/cache'
import { redirect } from 'next/navigation'

export async function updateProfile(formData: FormData) {
  const supabase = await createClient()
  
  // 验证用户
  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    redirect('/login')
  }

  // 更新用户资料
  const { error } = await supabase
    .from('user_profiles')
    .update({
      full_name: formData.get('full_name') as string,
      username: formData.get('username') as string,
      bio: formData.get('bio') as string,
    })
    .eq('id', user.id)

  if (error) {
    throw new Error(error.message)
  }

  revalidatePath('/profile')
}
```

---

## 🔒 安全最佳实践

### 1. 行级安全 (RLS) 配置

```sql
-- 确保所有自定义表都启用了 RLS
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;

-- 创建严格的策略
CREATE POLICY "Users can only access their own data" ON public.user_profiles
  USING (auth.uid() = id);
```

### 2. 环境变量安全

```bash
# Vercel 部署时的环境变量
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key

# 注意：ANON_KEY 是公开的，真正的安全由 RLS 保证
```

### 3. 类型安全

```typescript
// utils/supabase/client.ts - 带类型的客户端
import { createBrowserClient } from '@supabase/ssr'
import { Database } from '@/types/database'

export function createClient() {
  return createBrowserClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )
}
```

---

## 🚀 Vercel 部署配置

### 1. 自动部署模板

```bash
# 使用官方模板快速开始
npx create-next-app --example with-supabase my-app
```

### 2. 手动部署设置

1. **连接 Supabase 集成**：
   - 在 Vercel 项目设置中添加 Supabase 集成
   - 自动配置环境变量

2. **确保正确的构建设置**：
   ```json
   // package.json
   {
     "scripts": {
       "build": "next build",
       "start": "next start"
     }
   }
   ```

3. **验证部署**：
   - 检查环境变量是否正确设置
   - 测试认证流程
   - 验证 RLS 策略正常工作

---

## ✅ 生产就绪检查清单

### 开发阶段
- [ ] 使用 `@supabase/ssr` 包（不是 auth-helpers）
- [ ] 配置客户端和服务器 Supabase 客户端
- [ ] 实现中间件进行会话刷新
- [ ] 设置自定义用户扩展表
- [ ] 启用并配置 RLS 策略

### 部署阶段
- [ ] 在 Vercel 中正确配置环境变量
- [ ] 测试所有认证流程（登录、注册、登出）
- [ ] 验证受保护路由正常工作
- [ ] 测试用户数据的读取和更新
- [ ] 确保 RLS 策略在生产环境中正常工作

### 性能优化
- [ ] 使用 Server Components 进行初始数据获取
- [ ] 实现适当的缓存策略
- [ ] 优化数据库查询
- [ ] 使用 TypeScript 确保类型安全

---

## 🎯 总结与建议

### 选择理由

1. **官方推荐**：`@supabase/ssr` 是 Supabase 官方推荐的新方案
2. **Next.js 15 兼容**：专门为 Next.js 15 和 App Router 设计
3. **Vercel 优化**：完美支持 Vercel 部署环境
4. **生产就绪**：已经有大量生产环境成功案例
5. **社区支持**：活跃的社区和持续的更新

### 技术优势

- **SSR 友好**：原生支持服务器端渲染
- **性能优化**：利用 Server Components 减少客户端 JavaScript
- **安全性**：通过 RLS 和中间件提供多层安全保护
- **开发体验**：TypeScript 支持，清晰的 API 设计
- **扩展性**：易于与自定义用户表集成

**结论**：对于您的技术栈（Next.js 15 + Supabase + Vercel），强烈推荐使用 SSR 方案配合 `@supabase/ssr` 包。这是目前最稳定、最现代的解决方案。

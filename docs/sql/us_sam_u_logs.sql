create table public.us_sam_u_logs (
  id uuid primary key default gen_random_uuid(),
  auth_user_id uuid not null references auth.users(id),
  event_type text not null,           -- 行为类型，如 login、download、logout、register
  event_time timestamptz not null default now(),
  ip_address text,                    -- 行为发生时的IP
  user_agent text,                    -- 可选，记录设备/浏览器信息
  extra jsonb,                        -- 可选，行为相关的扩展信息（如下载文件名、页面路径等）
  session_id text,                    -- 会话ID，用于关联同一会话的多个行为
  referrer text,                      -- 来源页面URL
  page_url text,                      -- 当前页面URL
  duration integer check (duration >= 0 or duration is null)  -- 停留时长（秒）
);

-- 创建索引
create index idx_us_sam_u_logs_auth_user_id on public.us_sam_u_logs(auth_user_id);
create index idx_us_sam_u_logs_event_type on public.us_sam_u_logs(event_type);
create index idx_us_sam_u_logs_event_time on public.us_sam_u_logs(event_time);
create index idx_us_sam_u_logs_session_id on public.us_sam_u_logs(session_id);

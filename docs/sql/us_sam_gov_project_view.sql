-- 先删除已存在的视图（如果存在）
DROP VIEW IF EXISTS us_sam_gov_project_view;

-- 创建或替换视图，使用最简单的方法提供资源链接存在性信息
CREATE OR REPLACE VIEW us_sam_gov_project_view AS
SELECT
    id,
    posteddate AS posted_date,
    TO_CHAR(posteddate, 'YYYY-MM-DD') AS formatted_posted_date,
    TO_CHAR(responsedeadline, 'YYYY-MM-DD') AS formatted_response_deadline,
    solicitationnumber,
    title,
    fullparentpathname,
    type,
    setasidecode,
    poc_fullname,
    poc_email,
    awardee_name,
    -- 检测资源链接是否存在
    CASE
        WHEN resourcelinks IS NULL THEN 0
        WHEN CAST(resourcelinks AS TEXT) = '[]' THEN 0
        ELSE 1
    END AS has_resources,
    award_amount,
    created_at,
    updated_at,
    pop_country_code,
    pop_city_name
FROM 
    us_sam_gov
WHERE 
    title IS NOT NULL;

-- 授予公共角色对视图的查询权限
GRANT SELECT ON us_sam_gov_project_view TO PUBLIC;

-- 为视图添加注释
COMMENT ON VIEW us_sam_gov_project_view IS '客户项目视图，显示所有有标题的项目，包含资源链接存在性标志';

-- 为新增字段添加注释
COMMENT ON COLUMN us_sam_gov_project_view.has_resources IS '资源链接存在性标志：1表示存在资源链接，0表示不存在';

-- 启用RLS
ALTER TABLE us_sam_u_profiles ENABLE ROW LEVEL SECURITY;

-- 只允许本人查询自己的profile
CREATE POLICY "Users can view their own profile" ON us_sam_u_profiles
  FOR SELECT USING (auth.uid() = auth_user_id OR role = 'ADMIN');

-- 只允许本人或管理员更新自己的profile
CREATE POLICY "Users can update their own profile" ON us_sam_u_profiles
  FOR UPDATE USING (auth.uid() = auth_user_id OR role = 'ADMIN');

-- 只允许通过邀请码注册（可结合API逻辑控制）
-- 可根据业务需求细化更多策略



CREATE TABLE us_sam_u_profiles (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  auth_user_id uuid NOT NULL UNIQUE,         -- 关联 supabase.auth.users.id
  email text NOT NULL,                       -- 冗余存储，便于查询
  full_name text,
  role text NOT NULL DEFAULT 'USER',         -- 角色：ADMIN/USER/SUPER/扩展
  invite_code text,                          -- 邀请码（注册时填写/分配）
  invited_by uuid,                           -- 邀请人（可选，关联auth_user_id）
  subscription_start timestamp with time zone, -- 订阅开始时间
  subscription_end timestamp with time zone,   -- 订阅到期时间
  is_active boolean NOT NULL DEFAULT true,   -- 是否激活
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  last_login timestamp with time zone,
  -- SaaS扩展字段
  plan text DEFAULT 'basic',                 -- 订阅套餐（basic/pro/enterprise等）
  status text DEFAULT 'active',              -- 账户状态（active/expired/suspended等）
  trial_end timestamp with time zone,        -- 试用期结束时间
  payment_provider text,                     -- 支付渠道（stripe/paypal等）
  payment_customer_id text,                  -- 支付平台客户ID
  payment_subscription_id text,              -- 支付平台订阅ID
  metadata jsonb,                           -- 预留扩展字段
  CONSTRAINT fk_auth_user FOREIGN KEY (auth_user_id) REFERENCES auth.users(id) ON DELETE CASCADE
);
-- 启用RLS
ALTER TABLE us_sam_u_profiles ENABLE ROW LEVEL SECURITY;

-- 只允许本人查询自己的profile
CREATE POLICY "Users can view their own profile" ON us_sam_u_profiles
  FOR SELECT USING (auth.uid() = auth_user_id OR role = 'admin');

-- 只允许本人或管理员更新自己的profile
CREATE POLICY "Users can update their own profile" ON us_sam_u_profiles
  FOR UPDATE USING (auth.uid() = auth_user_id OR role = 'admin');

-- 只允许通过邀请码注册（可结合API逻辑控制）
-- 可根据业务需求细化更多策略
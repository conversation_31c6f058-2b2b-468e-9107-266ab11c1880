-- 如果表不存在，则创建最终目标表
CREATE TABLE IF NOT EXISTS us_sam_gov_archived (
    id SERIAL PRIMARY KEY,  -- 自增主键
    notice_id TEXT UNIQUE,  -- 设置为唯一键以避免重复
-- 基本信息字段
    title TEXT,
    sol_number TEXT,                   -- 原始名称: "Sol#"
    department_ind_agency TEXT,        -- 原始名称: "Department/Ind.Agency"
    cgac TEXT,                         -- 从INTEGER改为TEXT
    sub_tier TEXT,                     -- 原始名称: "Sub-Tier"
    fpds_code TEXT,                    -- 原始名称: "FPDS Code"，从INTEGER改为TEXT
    office TEXT,
    aac_code TEXT,                     -- 原始名称: "AAC Code"
    
    -- 日期相关字段（全部为TEXT以避免转换问题）
    posted_date TEXT,                  -- 原始名称: "PostedDate"
    type TEXT,
    base_type TEXT,                    -- 原始名称: "BaseType"
    archive_type TEXT,                 -- 原始名称: "ArchiveType"
    archive_date TEXT,                 -- 原始名称: "ArchiveDate"
    
    -- 设置和分类字段
    set_aside_code TEXT,               -- 原始名称: "SetASideCode"
    set_aside TEXT,                    -- 原始名称: "SetASide"
    response_dead_line TEXT,           -- 原始名称: "ResponseDeadLine"
    naics_code TEXT,                   -- 原始名称: "NaicsCode"，从INTEGER改为TEXT
    classification_code TEXT,          -- 原始名称: "ClassificationCode"
    
    -- 位置信息字段
    pop_street_address TEXT,           -- 原始名称: "PopStreetAddress"
    pop_city TEXT,                     -- 原始名称: "PopCity"
    pop_state TEXT,                    -- 原始名称: "PopState"
    pop_zip TEXT,                      -- 原始名称: "PopZip"，从NUMERIC改为TEXT
    pop_country TEXT,                  -- 原始名称: "PopCountry"
    
    -- 状态和奖励字段
    active TEXT,
    award_number TEXT,                 -- 原始名称: "AwardNumber"
    award_date TEXT,                   -- 原始名称: "AwardDate"
    award_amount TEXT,                 -- 原始名称: "Award$"
    awardee TEXT,
    
    -- 主要联系人字段
    primary_contact_title TEXT,        -- 原始名称: "PrimaryContactTitle"
    primary_contact_fullname TEXT,     -- 原始名称: "PrimaryContactFullname"
    primary_contact_email TEXT,        -- 原始名称: "PrimaryContactEmail"
    primary_contact_phone TEXT,        -- 原始名称: "PrimaryContactPhone"
    primary_contact_fax TEXT,          -- 原始名称: "PrimaryContactFax"
    
    -- 次要联系人字段
    secondary_contact_title TEXT,      -- 原始名称: "SecondaryContactTitle"
    secondary_contact_fullname TEXT,   -- 原始名称: "SecondaryContactFullname"
    secondary_contact_email TEXT,      -- 原始名称: "SecondaryContactEmail"
    secondary_contact_phone TEXT,      -- 原始名称: "SecondaryContactPhone"
    secondary_contact_fax TEXT,        -- 原始名称: "SecondaryContactFax"
    
    -- 组织和位置字段
    organization_type TEXT,            -- 原始名称: "OrganizationType"
    state TEXT,
    city TEXT,
    zip_code TEXT,                     -- 原始名称: "ZipCode"
    country_code TEXT,                 -- 原始名称: "CountryCode"
    
    -- 链接和描述字段
    additional_info_link TEXT,         -- 原始名称: "AdditionalInfoLink"
    link TEXT,
    description TEXT,

   
    -- 元数据字段（追踪导入和更新）
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_us_sam_gov_archived_notice_id ON us_sam_gov_archived(notice_id);
CREATE INDEX IF NOT EXISTS idx_us_sam_gov_archived_sol_number ON us_sam_gov_archived(sol_number);
CREATE INDEX IF NOT EXISTS idx_us_sam_gov_archived_posted_date ON us_sam_gov_archived(posted_date);

-- 创建或替换用于前端查询的存档数据视图
CREATE OR REPLACE VIEW us_sam_gov_archived_frontend_view AS
SELECT
    notice_id,                      -- 通知ID，唯一标识符
    title,                          -- 机会标题
    sol_number,                     -- 招标编号
    department_ind_agency,          -- 部门/独立机构
    sub_tier,                       -- 子层级机构
    office,                         -- 办公室
    
    -- 将 posted_date 格式化为 'YYYY-MM-DD'
    to_char(posted_date, 'YYYY-MM-DD') AS formatted_posted_date, 
    -- 保留原始的 posted_date 以便进行范围查询
    posted_date,                    
    
    type,                           -- 类型
    base_type,                      -- 基本类型
    archive_type,                   -- 存档类型
    archive_date,                   -- 存档日期 (TEXT)
    set_aside_code,                 -- 预留代码
    set_aside,                      -- 预留类型
    response_dead_line,             -- 响应截止日期 (TEXT)
    naics_code,                     -- NAICS 代码
    classification_code,            -- 分类代码
    pop_street_address,             -- 履行地点街道地址
    pop_city,                       -- 履行地点城市
    pop_state,                      -- 履行地点州
    pop_zip,                        -- 履行地点邮编
    pop_country,                    -- 履行地点国家
    active,                         -- 是否活动
    award_number,                   -- 奖励编号
    award_date,                     -- 奖励日期 (TEXT)
    
    -- 尝试格式化奖励金额，处理带 $ 和 , 的情况
    CASE
        WHEN award_amount ~ '^\$?[0-9,]+(\.[0-9]+)?$' THEN -- 检查是否为货币格式 (可选$, 含逗号)
            '$' || to_char(regexp_replace(award_amount, '[^0-9.]', '', 'g')::NUMERIC, 'FM999,999,999,990.00')
        WHEN award_amount ~ '^[0-9]+(\.[0-9]+)?$' THEN -- 检查是否为纯数字格式
            to_char(award_amount::NUMERIC, 'FM999,999,999,990.00') 
        ELSE award_amount -- 如果都不是，则返回原始文本
    END AS formatted_award_amount,
    
    awardee,                        -- 获奖者
    primary_contact_fullname,       -- 主要联系人全名
    primary_contact_email,          -- 主要联系人邮箱
    primary_contact_phone,          -- 主要联系人电话
    organization_type,              -- 组织类型
    state,                          -- 州
    city,                           -- 城市
    zip_code,                       -- 邮编
    country_code,                   -- 国家代码
    additional_info_link,           -- 附加信息链接
    link,                           -- 主链接
    description,                    -- 描述
    created_at,                     -- 创建时间
    updated_at,                     -- 更新时间
    title_search_vector             -- 全文搜索向量 (用于标题搜索)
FROM 
    us_sam_gov_archived_partitioned; -- 直接查询分区父表

-- 添加视图注释
COMMENT ON VIEW us_sam_gov_archived_frontend_view IS '提供给前端使用的存档机会数据视图，基于按年份分区的表 us_sam_gov_archived_partitioned，利用分区提高查询效率。';

-- 添加关键列注释
COMMENT ON COLUMN us_sam_gov_archived_frontend_view.notice_id IS '通知ID，作为记录的唯一标识符。';
COMMENT ON COLUMN us_sam_gov_archived_frontend_view.title IS '机会或合同的标题。';
COMMENT ON COLUMN us_sam_gov_archived_frontend_view.formatted_posted_date IS '格式化为 YYYY-MM-DD 的发布日期。';
COMMENT ON COLUMN us_sam_gov_archived_frontend_view.posted_date IS '原始的发布日期和时间（带时区），可用于精确的日期范围过滤。';
COMMENT ON COLUMN us_sam_gov_archived_frontend_view.formatted_award_amount IS '格式化后的奖励金额（带$和千位分隔符），能处理多种输入格式，非数字时显示原值。';
COMMENT ON COLUMN us_sam_gov_archived_frontend_view.title_search_vector IS '预计算的标题全文搜索向量，用于加速标题搜索。';

-- 授予公共查询权限 (根据需要调整权限)
GRANT SELECT ON TABLE public.us_sam_gov_archived_frontend_view TO PUBLIC;

-- 提示：为了充分利用分区性能，前端查询此视图时应尽可能包含对 `posted_date` 列的过滤条件。 
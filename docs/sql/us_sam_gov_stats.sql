-- 创建用于Dashboard统计的视图

-- 创建每日机会数据视图(最近30天)
CREATE OR REPLACE VIEW us_sam_gov_daily_opportunities AS
SELECT 
  posted_date::date AS date,
  COUNT(*) AS count
FROM 
  us_sam_gov_project_view
WHERE
  posted_date IS NOT NULL
  AND posted_date::date >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY 
  posted_date::date
ORDER BY 
  date DESC;

-- 创建简化版统计视图，每日数据记录
CREATE OR REPLACE VIEW us_sam_gov_stats AS
WITH daily_opportunities AS (
  SELECT 
    posted_date::date AS date,
    COUNT(*) AS count
  FROM 
    us_sam_gov_project_view
  WHERE
    posted_date IS NOT NULL
    AND posted_date::date >= CURRENT_DATE - INTERVAL '30 days'
  GROUP BY 
    posted_date::date
),
-- 计算每日联系人统计
daily_contacts AS (
  SELECT 
    posted_date::date AS date,
    COUNT(*) AS count
  FROM 
    us_sam_gov_contact_view
  WHERE
    posted_date IS NOT NULL
    AND posted_date::date >= CURRENT_DATE - INTERVAL '30 days'
  GROUP BY 
    posted_date::date
),
-- 计算每日供应商统计
daily_awardees AS (
  SELECT 
    posted_date::date AS date,
    COUNT(*) AS count
  FROM 
    us_sam_gov_awardee_view
  WHERE
    posted_date IS NOT NULL
    AND posted_date::date >= CURRENT_DATE - INTERVAL '30 days'
  GROUP BY 
    posted_date::date
)
SELECT
  ROW_NUMBER() OVER (ORDER BY d.date DESC) AS id,
  d.date,
  d.count AS opportunities_stats,
  -- 联系人统计数据
  COALESCE(dc.count, 0) AS people_stats,
  -- 供应商统计数据
  COALESCE(da.count, 0) AS awardees_stats
FROM 
  daily_opportunities d
LEFT JOIN
  daily_contacts dc ON d.date = dc.date
LEFT JOIN
  daily_awardees da ON d.date = da.date
ORDER BY 
  d.date DESC;
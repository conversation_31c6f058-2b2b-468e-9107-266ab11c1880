CREATE OR REPLACE VIEW us_sam_gov_project_detail_view AS
SELECT
    -- 基本标识字段
    us_sam_gov.id,
    us_sam_gov.noticeid::text,  -- 确保 noticeid 为文本类型
    us_sam_gov.title,
    us_sam_gov.solicitationnumber,
    us_sam_gov.type,
    us_sam_gov.basetype,
    us_sam_gov.active,
    us_sam_gov.uilink,
    
    -- 时间相关字段
    us_sam_gov.posteddate,
    TO_CHAR(us_sam_gov.posteddate, 'YYYY-MM-DD') AS formatted_posted_date,
    us_sam_gov.archivedate,
    TO_CHAR(us_sam_gov.archivedate, 'YYYY-MM-DD') AS formatted_archive_date,
    us_sam_gov.archivetype,
    us_sam_gov.responsedeadline,
    TO_CHAR(us_sam_gov.responsedeadline, 'YYYY-MM-DD HH24:MI:SS') AS formatted_response_deadline,
    us_sam_gov.created_at,
    us_sam_gov.updated_at,
    
    -- 分类信息
    us_sam_gov.naicscode,
    us_sam_gov.classificationcode,
    us_sam_gov.fullparentpathcode,
    us_sam_gov.fullparentpathname,
    us_sam_gov.organizationtype,
    
    -- 描述信息
    -- us_sam_gov.description AS basic_description,  -- 重命名为basic_description
    CASE 
        WHEN us_sam_gov_description.description_detail IS NULL THEN NULL
        WHEN us_sam_gov_description.description_detail::json->>'error' = 'true' THEN NULL
        ELSE us_sam_gov_description.description_detail::json->>'description'
    END AS detailed_description,  -- 重命名为detailed_description
    
    -- 设置相关
    us_sam_gov.setaside,
    us_sam_gov.setasidecode,
    
    -- 办公室地址信息
    us_sam_gov.office_city,
    us_sam_gov.office_state,
    us_sam_gov.office_zip,
    us_sam_gov.office_countrycode,
    -- us_sam_gov.officeaddress,
    
    -- 联系人信息
    us_sam_gov.poc_type,
    us_sam_gov.poc_fullname,
    us_sam_gov.poc_email,
    us_sam_gov.poc_phone,
    us_sam_gov.poc_fax,
    us_sam_gov.poc_title,
    us_sam_gov.poc_additionalinfo,
    us_sam_gov.poc_additionalinfo_content,
    -- us_sam_gov.pointofcontact,
    
    -- 履行地点信息
    us_sam_gov.pop_city_code,
    us_sam_gov.pop_city_name,
    us_sam_gov.pop_state_code,
    us_sam_gov.pop_state_name,
    us_sam_gov.pop_country_code,
    us_sam_gov.pop_country_name,
    us_sam_gov.pop_zip,
    us_sam_gov.pop_streetaddress,
    us_sam_gov.pop_streetaddress2,
    -- us_sam_gov.placeofperformance,
    
    -- 奖项相关信息
    us_sam_gov.award_date,
    us_sam_gov.award_amount,
    us_sam_gov.award_number,
    
    -- 获奖方信息
    us_sam_gov.awardee_name,
    us_sam_gov.awardee_ueisam,
    us_sam_gov.awardee_city_code,
    us_sam_gov.awardee_city_name,
    us_sam_gov.awardee_state_code,
    us_sam_gov.awardee_state_name,
    us_sam_gov.awardee_country_code,
    us_sam_gov.awardee_country_name,
    us_sam_gov.awardee_zip,
    us_sam_gov.awardee_streetaddress,
    us_sam_gov.awardee_streetaddress2,
    
    -- 其他链接
    us_sam_gov.additionalinfolink,
    us_sam_gov.resourcelinks
    
    -- 原始数据
    -- us_sam_gov.raw_data
FROM 
    us_sam_gov
LEFT JOIN 
    us_sam_gov_description ON us_sam_gov.noticeid = us_sam_gov_description.noticeid;

-- 授予公共角色对新视图的查询权限
GRANT SELECT ON us_sam_gov_project_detail_view TO PUBLIC;

-- 为视图添加注释
COMMENT ON VIEW us_sam_gov_project_detail_view IS '项目详细信息视图，包含所有物理表字段和详细描述信息，专为详细页面展示设计';
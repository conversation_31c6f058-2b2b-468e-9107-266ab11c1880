-- 创建或替换视图，不使用表别名，并处理JSON格式的description字段
CREATE OR REPLACE VIEW us_sam_gov_project_view AS
SELECT
    us_sam_gov.id,
    us_sam_gov.posteddate AS posted_date,
    TO_CHAR(us_sam_gov.posteddate, 'YYYY-MM-DD') AS formatted_posted_date,
    TO_CHAR(us_sam_gov.responsedeadline, 'YYYY-MM-DD') AS formatted_response_deadline,
    us_sam_gov.solicitationnumber,
    us_sam_gov.title,
    -- 添加title_with_metadata字段，用于前端展示
    us_sam_gov.title AS title_with_metadata,
    us_sam_gov.fullparentpathname,
    us_sam_gov.type,
    us_sam_gov.setasidecode,
    us_sam_gov.awardee_name,
    us_sam_gov.resourcelinks,
    us_sam_gov.created_at,
    us_sam_gov.updated_at,
    us_sam_gov.pop_country_code,
    us_sam_gov.award_amount,

    -- 格式化处理JSON格式的description_detail字段
    CASE 
        WHEN us_sam_gov_description.description_detail IS NULL THEN NULL
        WHEN us_sam_gov_description.description_detail::json->>'error' = 'true' THEN NULL
        ELSE us_sam_gov_description.description_detail::json->>'description'
    END AS description_detail,

        -- 使用CASE语句替代GREATEST函数来处理最后修改时间
    CASE 
        WHEN us_sam_gov.updated_at > us_sam_gov_description.updated_at 
            OR us_sam_gov_description.updated_at IS NULL 
        THEN us_sam_gov.updated_at
        ELSE us_sam_gov_description.updated_at
    END AS last_modified

FROM 
    us_sam_gov
LEFT JOIN 
    us_sam_gov_description ON us_sam_gov.noticeid = us_sam_gov_description.noticeid
WHERE 
    us_sam_gov.title IS NOT NULL;

-- 授予公共角色对视图的查询权限
GRANT SELECT ON us_sam_gov_project_view TO PUBLIC;

-- 为视图添加注释
COMMENT ON VIEW us_sam_gov_project_view IS '客户项目视图，显示所有有标题的项目，包含格式化的详细描述';

-- 为新增的last_modified字段添加注释
COMMENT ON COLUMN us_sam_gov_project_view.last_modified IS '数据最后更新时间戳，用于前端缓存控制';

-- 步骤1：创建主数据表
CREATE TABLE us_sam_gov_fullcsv (
    notice_id TEXT PRIMARY KEY,           -- 原"NoticeId"    
    title TEXT,
    sol_number TEXT,                      -- 原"Sol#"
    department_ind_agency TEXT,
    cgac TEXT,
    sub_tier TEXT,
    fpds_code TEXT,
    office TEXT,
    aac_code TEXT,
    posted_date TEXT,                -- 时间类型提高日期查询效率
    type TEXT,
    base_type TEXT,
    archive_type TEXT,
    archive_date TEXT,               -- 时间类型
    set_aside_code TEXT,
    set_aside TEXT,
    response_dead_line TEXT,              -- 日期类型
    naics_code TEXT,
    classification_code TEXT,
    pop_street_address TEXT,
    pop_city TEXT,
    pop_state TEXT,
    pop_zip NUMERIC,                      -- 数值类型便于范围查询
    pop_country TEXT,
    active TEXT,
    award_number TEXT,
    award_date TEXT,                 -- 时间类型
    award_amount TEXT,                 -- 原"Award$"，数值类型便于计算
    awardee TEXT,
    primary_contact_title TEXT,
    primary_contact_fullname TEXT,
    primary_contact_email TEXT,
    primary_contact_phone TEXT,           -- 电话号码保留为文本
    primary_contact_fax TEXT,
    secondary_contact_title TEXT,
    secondary_contact_fullname TEXT,
    secondary_contact_email TEXT,
    secondary_contact_phone TEXT,
    secondary_contact_fax TEXT,
    organization_type TEXT,
    state TEXT,
    city TEXT,
    zip_code TEXT,                        -- 邮编保留文本格式
    country_code TEXT,
    additional_info_link TEXT,
    link TEXT,
    description TEXT,
    
    -- 添加元数据字段，便于追踪
    import_date TIMESTAMP DEFAULT NOW(),  -- 记录数据导入时间
    last_updated TIMESTAMP DEFAULT NOW()  -- 记录最后更新时间
);

-- 创建辅助索引以提高查询性能
CREATE INDEX idx_us_sam_gov_fullcsv_posted_date ON us_sam_gov_fullcsv(posted_date);
CREATE INDEX idx_us_sam_gov_fullcsv_naics_code ON us_sam_gov_fullcsv(naics_code);
CREATE INDEX idx_us_sam_gov_fullcsv_department ON us_sam_gov_fullcsv(department_ind_agency);

-- 添加表注释
COMMENT ON TABLE us_sam_gov_fullcsv IS 'SAM.gov合同机会数据，来自CSV导入';
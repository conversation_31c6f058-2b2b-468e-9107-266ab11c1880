CREATE TABLE us_sam_gov_archived_partitioned (
    -- 主键
    notice_id TEXT NOT NULL,
    
    -- 基本信息字段
    title TEXT,
    sol_number TEXT,
    department_ind_agency TEXT,
    cgac TEXT,
    sub_tier TEXT,
    fpds_code TEXT,
    office TEXT,
    aac_code TEXT,
    
    -- 日期相关字段（注意这里改为TIMESTAMP WITH TIME ZONE）
    posted_date TIMESTAMP WITH TIME ZONE NOT NULL,
    type TEXT,
    base_type TEXT,
    archive_type TEXT,
    archive_date TEXT,
    
    -- 设置和分类字段
    set_aside_code TEXT,
    set_aside TEXT,
    response_dead_line TEXT,
    naics_code TEXT,
    classification_code TEXT,
    
    -- 位置信息字段
    pop_street_address TEXT,
    pop_city TEXT,
    pop_state TEXT,
    pop_zip TEXT,
    pop_country TEXT,
    
    -- 状态和奖励字段
    active TEXT,
    award_number TEXT,
    award_date TEXT,
    award_amount TEXT,
    awardee TEXT,
    
    -- 主要联系人字段
    primary_contact_title TEXT,
    primary_contact_fullname TEXT,
    primary_contact_email TEXT,
    primary_contact_phone TEXT,
    primary_contact_fax TEXT,
    
    -- 次要联系人字段
    secondary_contact_title TEXT,
    secondary_contact_fullname TEXT,
    secondary_contact_email TEXT,
    secondary_contact_phone TEXT,
    secondary_contact_fax TEXT,
    
    -- 组织和位置字段
    organization_type TEXT,
    state TEXT,
    city TEXT,
    zip_code TEXT,
    country_code TEXT,
    
    -- 链接和描述字段
    additional_info_link TEXT,
    link TEXT,
    description TEXT,
    
    -- 元数据字段（追踪导入和更新）
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- 全文搜索向量
    title_search_vector tsvector,
    
    PRIMARY KEY (notice_id, posted_date)
) PARTITION BY RANGE (posted_date);
create table public.us_sam_u_invite_codes (
  id uuid primary key default gen_random_uuid(), -- 唯一主键
  code text not null unique,                    -- 邀请码字符串，唯一
  is_used boolean not null default false,       -- 是否已被使用
  used_by uuid references auth.users(id),       -- 使用者（auth 用户ID），可为空
  created_by uuid references auth.users(id),    -- 邀请人（auth 用户ID），可为空
  usage_limit int default 1,                    -- 使用次数上限，默认为1
  usage_count int not null default 0,           -- 已使用次数
  expires_at timestamptz,                       -- 过期时间，可为空
  created_at timestamptz not null default now(),-- 创建时间
  updated_at timestamptz not null default now() -- 更新时间
);

-- 用于自动更新时间戳
create or replace function public.update_us_sam_u_invite_codes_updated_at()
returns trigger as $$
begin
  new.updated_at = now();
  return new;
end;
$$ language plpgsql;

create trigger trigger_update_us_sam_u_invite_codes_updated_at
before update on public.us_sam_u_invite_codes
for each row
execute procedure public.update_us_sam_u_invite_codes_updated_at();

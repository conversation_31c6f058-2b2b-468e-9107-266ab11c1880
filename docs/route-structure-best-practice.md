# Next.js 目录结构与权限路由分组保护最佳实践

## 1. 公开页面与受保护页面分组
- 公开页面（如 login、register、forgot-password、reset-password）直接放在 `src/app/` 下一级目录，便于无权限访问。
- 受保护页面统一放在 `(protected)` 分组目录下，所有需要登录态的主界面页面均归于此，结构清晰。

## 2. 权限保护实现方式
- 仅需在 `(protected)/layout.tsx` 实现一次权限检查逻辑，所有子页面自动继承，无需每页重复判断。
- 公开页面无需 layout.tsx 权限保护，减少冗余代码。

## 3. 结构优势
- 目录层级清晰，权限边界一目了然，维护成本低。
- 新增公开或受保护页面时，直接按分组放置，无需额外配置。
- 权限逻辑集中，便于统一升级和扩展。

## 4. 推荐目录结构示例

```
src/app/
  login/
  register/
  forgot-password/
  reset-password/
  (protected)/
    layout.tsx  // 权限检查
    dashboard/
    people/
    settings/
    ...
```

## 5. 最佳实践总结
- 采用分组路由与 layout.tsx 权限保护，极大提升项目可维护性与安全性。
- 公开与受保护页面分离，权限边界清晰，适合中大型项目团队协作。
- 推荐所有 Next.js 项目采用此结构作为默认模板。 
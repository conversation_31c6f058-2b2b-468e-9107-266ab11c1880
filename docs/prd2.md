我将为您生成完整的项目文档。首先让我更新 PRD 文档，然后创建技术设计文档、Cursor Rules 和项目目录结构。

<artifacts>
<artifact identifier="hiddengov-prd-v2" type="text/markdown" title="HiddenGov 产品需求文档 (PRD) - 完整版">
# HiddenGov 产品需求文档 (PRD)

## 1. 产品概述

### 1.1 产品定位
HiddenGov 是一个面向企业用户的美国政府采购信息服务平台，通过整合 SAM.gov 数据，为用户提供高效的采购信息查询、分析和导出服务。平台采用封闭的 B2B 模式，通过邀请码机制严格控制用户准入。

### 1.2 核心价值
- 突破 SAM.gov 原生界面的查询限制，提供更灵活的数据检索
- 整合分散的政府采购信息，形成统一的数据视图
- 为企业参与美国政府采购提供情报支持和决策依据
- 通过订阅模式提供持续的数据服务

### 1.3 目标用户
- 美国政府承包商和供应商
- 国际贸易公司和咨询机构
- 商业情报分析公司
- 寻求美国市场机会的企业

## 2. 功能需求

### 2.1 用户系统

#### 2.1.1 注册流程
- 输入有效邀请码才能进入注册页面
- 填写邮箱、密码、全名等基本信息
- 通过 Cloudflare Turnstile 人机验证
- 系统自动创建用户档案并记录邀请关系
- 新用户默认获得7天试用期

#### 2.1.2 登录认证
- 邮箱密码登录方式
- 支持"记住我"功能（30天有效）
- 登录失败锁定机制（5次失败锁定30分钟）
- 所有登录行为记录到日志表
- 会话超时自动登出（24小时）

#### 2.1.3 用户角色权限
- **普通用户（USER）**
  - 查看项目列表和详情
  - 使用搜索和筛选功能
  - 导出CSV数据
  - 查看个人使用记录
  
- **管理员（ADMIN）**
  - 所有普通用户权限
  - 查看和管理所有用户
  - 创建和管理邀请码
  - 查看系统日志
  
- **超级管理员（SUPER）**
  - 所有管理员权限
  - 修改用户角色
  - 系统配置管理

### 2.2 数据查询模块

#### 2.2.1 项目列表页
- **数据源**：us_sam_gov_project_view 视图
- **默认排序**：按发布日期降序
- **分页设置**：每页20条，可选10/20/50/100条
- **展示字段**：
  - 发布日期（formatted_posted_date）
  - 项目编号（solicitationnumber）
  - 项目名称（title）
  - 采购机构（fullparentpathname）
  - 项目类型（type）
  - 联系人（poc_fullname）
  - 中标方（awardee_name）
  - 资源标识（has_resources）

#### 2.2.2 高级筛选
- 日期范围筛选（发布日期、截止日期）
- 关键词搜索（标题、编号）
- 机构筛选（下拉选择）
- 项目类型筛选
- 是否有资源附件
- 组合条件筛选

#### 2.2.3 项目详情页
- **数据源**：us_sam_gov_project_detail_view 视图
- **信息模块**：
  - 基本信息（标题、编号、类型、状态）
  - 时间信息（发布、截止、归档日期）
  - 机构信息（采购方详细信息）
  - 联系人信息（主要和次要联系人）
  - 履行地点信息
  - 中标信息（如有）
  - 详细描述（detailed_description）
  - 相关链接和资源

#### 2.2.4 历史数据查询
- **数据源**：us_sam_gov_archived_frontend_view 视图
- 按年份快速筛选（利用分区表优化）
- 支持跨年份范围查询
- 历史数据统计图表

### 2.3 数据导出功能

#### 2.3.1 导出格式
- CSV 格式（UTF-8 编码，支持 Excel 打开）
- 包含当前筛选条件下的所有数据
- 自定义选择导出字段

#### 2.3.2 导出限制（一期暂不实现）
- 单次导出上限设置
- 每日导出次数限制
- 导出记录日志

### 2.4 邀请码管理

#### 2.4.1 邀请码创建（管理员功能）
- 批量生成邀请码
- 设置使用次数上限（默认1次）
- 设置过期时间（可选）
- 自定义邀请码前缀

#### 2.4.2 邀请码管理
- 查看所有邀请码列表
- 显示使用状态和使用者
- 禁用/启用邀请码
- 查看邀请关系链

### 2.5 用户管理（管理员功能）

#### 2.5.1 用户列表
- 显示所有注册用户
- 查看用户详细信息
- 筛选和搜索用户
- 批量操作功能

#### 2.5.2 用户操作
- 激活/禁用账户
- 修改用户信息
- 重置用户密码
- 延长试用期
- 查看用户行为日志

### 2.6 系统监控

#### 2.6.1 数据统计仪表板
- **数据源**：us_sam_gov_stats 视图
- 最近30天每日新增项目数
- 按类型分布统计
- 用户活跃度统计
- 系统使用趋势

#### 2.6.2 日志查询
- **数据源**：us_sam_u_logs 表
- 按用户筛选
- 按行为类型筛选
- 按时间范围筛选
- 导出日志功能

### 2.7 个人中心

#### 2.7.1 个人信息
- 查看和修改个人资料
- 修改密码
- 查看订阅状态
- 使用 DiceBear 生成个性化头像

#### 2.7.2 使用记录
- 查询历史记录
- 导出历史记录
- 使用统计数据

## 3. 界面设计规范

### 3.1 整体风格
- 专业的商务风格，以数据展示为主
- 支持亮色/暗色模式切换
- 响应式设计，支持桌面和移动设备
- 使用 shadcn-ui 组件保持界面一致性

### 3.2 关键页面布局
- **登录页**：简洁的单栏布局，突出品牌
- **数据列表页**：左侧筛选面板，右侧数据表格
- **详情页**：分块展示信息，支持锚点导航
- **管理后台**：顶部导航 + 侧边菜单

### 3.3 交互规范
- 所有表单使用 Zod 进行实时验证
- 加载状态使用骨架屏
- 操作反馈使用 Toast 提示
- 危险操作需要二次确认

## 4. 技术规范

### 4.1 前端架构
- Next.js 15 App Router（SSR 优先）
- TypeScript 严格模式
- Tailwind CSS 原子化样式
- 组件使用 shadcn-ui
- 图标使用 Lucide Icons
- 包管理使用 pnpm

### 4.2 数据层
- Supabase 作为 BaaS 平台
- 启用 Row Level Security
- 管理功能使用 service key
- 用户功能使用 anon key
- 实时订阅预留接口

### 4.3 安全措施
- Cloudflare Turnstile 防机器人
- API 请求频率限制
- 敏感操作日志记录
- 定期安全审计

## 5. 部署和运维

### 5.1 部署环境
- 生产环境部署在 Vercel
- 使用 hiddengov.com 域名
- 配置 CDN 加速
- 自动 SSL 证书

### 5.2 监控指标
- 服务可用性 > 99.9%
- 页面加载时间 < 2秒
- API 响应时间 < 500ms
- 错误率 < 0.1%

## 6. 项目计划

### 第一阶段：基础功能（4周）
- 用户认证系统
- 项目列表和详情页
- 基础搜索筛选
- 响应式布局

### 第二阶段：核心功能（3周）
- 邀请码系统
- CSV 数据导出
- 高级筛选功能
- 个人中心

### 第三阶段：管理功能（3周）
- 用户管理后台
- 邀请码管理
- 系统日志查询
- 数据统计仪表板

### 第四阶段：优化完善（2周）
- 性能优化
- 用户体验改进
- 文档完善
- 上线准备

## 7. 成功指标

- 用户注册转化率 > 70%（有效邀请码）
- 日活跃用户比例 > 40%
- 平均会话时长 > 10分钟
- 数据查询成功率 > 99%
- 用户满意度 > 85%
</artifact>
</artifacts>

<artifacts>
<artifact identifier="hiddengov-tdd" type="text/markdown" title="HiddenGov 技术设计文档 (TDD)">
# HiddenGov 技术设计文档 (TDD)

## 1. 系统架构概述

### 1.1 整体架构
HiddenGov 采用现代化的 JAMstack 架构，前端使用 Next.js 15 的 App Router 实现服务端渲染，后端依托 Supabase 提供的 BaaS 服务。这种架构确保了系统的高性能、可扩展性和安全性。

### 1.2 技术选型理由
- **Next.js 15 App Router**：提供优秀的 SSR/SSG 能力，改善 SEO 和首屏加载性能
- **Supabase**：内置认证系统、RLS 权限控制、实时订阅能力
- **TypeScript**：类型安全，减少运行时错误，提升开发效率
- **shadcn-ui**：基于 Radix UI 的组件库，可定制性强，无需额外依赖
- **Tailwind CSS**：原子化 CSS，减少样式冲突，提高开发速度

### 1.3 系统分层
```
┌─────────────────────────────────────────┐
│          客户端浏览器                      │
├─────────────────────────────────────────┤
│     Next.js App (Vercel Edge)           │
│  ┌─────────────┬──────────────────┐     │
│  │  App Router │  Middleware      │     │
│  │  SSR Pages  │  Auth Guard      │     │
│  └─────────────┴──────────────────┘     │
├─────────────────────────────────────────┤
│         Supabase Services               │
│  ┌──────────┬────────┬──────────┐      │
│  │   Auth   │  RLS   │ Storage  │      │
│  │  System  │ Rules  │  (R2)    │      │
│  └──────────┴────────┴──────────┘      │
├─────────────────────────────────────────┤
│       PostgreSQL Database               │
│    (Partitioned Tables + Views)         │
└─────────────────────────────────────────┘
```

## 2. 数据库设计

### 2.1 核心数据表关系
```
┌─────────────────┐     ┌──────────────────────┐
│   auth.users    │────<│  us_sam_u_profiles   │
└─────────────────┘     └──────────────────────┘
         │                         │
         │                         │
         ▼                         ▼
┌─────────────────┐     ┌──────────────────────┐
│ us_sam_u_logs   │     │us_sam_u_invite_codes │
└─────────────────┘     └──────────────────────┘

数据视图层：
┌─────────────────────────┐
│ us_sam_gov_project_view │
├─────────────────────────┤
│us_sam_gov_detail_view   │
├─────────────────────────┤
│us_sam_gov_archived_view │
├─────────────────────────┤
│   us_sam_gov_stats      │
└─────────────────────────┘
```

### 2.2 RLS 策略设计

#### 2.2.1 用户档案表 (us_sam_u_profiles)
```sql
-- 用户只能查看自己的档案
CREATE POLICY "select_own_profile" ON us_sam_u_profiles
  FOR SELECT USING (auth.uid() = auth_user_id);

-- 管理员可以查看所有档案
CREATE POLICY "admin_select_all_profiles" ON us_sam_u_profiles
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM us_sam_u_profiles
      WHERE auth_user_id = auth.uid() 
      AND role IN ('ADMIN', 'SUPER')
    )
  );
```

#### 2.2.2 数据视图访问控制
```sql
-- 只有激活的用户可以访问数据视图
CREATE POLICY "active_users_only" ON us_sam_gov_project_view
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM us_sam_u_profiles
      WHERE auth_user_id = auth.uid() 
      AND is_active = true
      AND (subscription_end IS NULL OR subscription_end > NOW())
    )
  );
```

### 2.3 数据库索引优化
```sql
-- 用户表索引
CREATE INDEX idx_profiles_email ON us_sam_u_profiles(email);
CREATE INDEX idx_profiles_role ON us_sam_u_profiles(role);
CREATE INDEX idx_profiles_status ON us_sam_u_profiles(is_active, subscription_end);

-- 日志表索引
CREATE INDEX idx_logs_user_time ON us_sam_u_logs(auth_user_id, event_time DESC);
CREATE INDEX idx_logs_event_type ON us_sam_u_logs(event_type, event_time DESC);

-- 项目视图索引（物化视图优化）
CREATE INDEX idx_project_posted_date ON us_sam_gov_project_view(posted_date DESC);
CREATE INDEX idx_project_title_gin ON us_sam_gov_project_view USING gin(title gin_trgm_ops);
```

## 3. 认证与授权

### 3.1 认证流程
```typescript
// 认证流程示意
interface AuthFlow {
  // 1. 邀请码验证
  validateInviteCode(code: string): Promise<boolean>
  
  // 2. Turnstile 验证
  verifyTurnstile(token: string): Promise<boolean>
  
  // 3. 用户注册
  signUp(email: string, password: string, metadata: UserMetadata): Promise<User>
  
  // 4. 创建用户档案
  createProfile(userId: string, inviteCode: string): Promise<Profile>
  
  // 5. 记录注册日志
  logRegistration(userId: string, ip: string): Promise<void>
}
```

### 3.2 权限中间件
```typescript
// middleware.ts 核心逻辑
export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  
  // 公开路径
  const publicPaths = ['/login', '/register', '/']
  if (publicPaths.includes(pathname)) return NextResponse.next()
  
  // 验证会话
  const session = await getSession(request)
  if (!session) return NextResponse.redirect('/login')
  
  // 管理员路径权限检查
  const adminPaths = ['/admin', '/users', '/invite-codes']
  if (adminPaths.some(path => pathname.startsWith(path))) {
    const profile = await getProfile(session.user.id)
    if (!['ADMIN', 'SUPER'].includes(profile.role)) {
      return NextResponse.redirect('/403')
    }
  }
  
  return NextResponse.next()
}
```

## 4. API 设计

### 4.1 API 路由结构
```
/api/
├── auth/
│   ├── register        # POST - 用户注册
│   ├── login          # POST - 用户登录
│   ├── logout         # POST - 用户登出
│   └── validate-code  # POST - 验证邀请码
├── projects/
│   ├── list           # GET - 项目列表
│   ├── [id]          # GET - 项目详情
│   └── export        # POST - 导出数据
├── users/
│   ├── profile       # GET/PUT - 用户档案
│   ├── list          # GET - 用户列表（管理员）
│   └── [id]/status   # PUT - 用户状态（管理员）
├── invite-codes/
│   ├── create        # POST - 创建邀请码
│   ├── list          # GET - 邀请码列表
│   └── [code]/disable # PUT - 禁用邀请码
└── stats/
    └── dashboard     # GET - 统计数据
```

### 4.2 API 响应格式
```typescript
// 统一响应格式
interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: {
    code: string
    message: string
    details?: any
  }
  metadata?: {
    timestamp: string
    requestId: string
  }
}

// 分页响应
interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
  }
}
```

### 4.3 错误处理
```typescript
// 错误代码定义
enum ErrorCode {
  // 认证错误
  AUTH_INVALID_CREDENTIALS = 'AUTH001',
  AUTH_SESSION_EXPIRED = 'AUTH002',
  AUTH_INSUFFICIENT_PERMISSIONS = 'AUTH003',
  
  // 业务错误
  INVITE_CODE_INVALID = 'BIZ001',
  INVITE_CODE_EXPIRED = 'BIZ002',
  USER_QUOTA_EXCEEDED = 'BIZ003',
  
  // 系统错误
  DATABASE_ERROR = 'SYS001',
  EXTERNAL_SERVICE_ERROR = 'SYS002',
}
```

## 5. 前端架构

### 5.1 目录结构
```
src/
├── app/                    # Next.js App Router
│   ├── (auth)/            # 认证相关页面组
│   │   ├── login/
│   │   └── register/
│   ├── (dashboard)/       # 主应用页面组
│   │   ├── projects/
│   │   ├── profile/
│   │   └── layout.tsx
│   ├── admin/             # 管理后台
│   │   ├── users/
│   │   ├── invite-codes/
│   │   └── layout.tsx
│   └── api/               # API 路由
├── components/            # 可复用组件
│   ├── ui/               # shadcn-ui 组件
│   ├── features/         # 业务组件
│   └── layouts/          # 布局组件
├── lib/                  # 工具库
│   ├── supabase/        # Supabase 客户端
│   ├── utils/           # 工具函数
│   └── validations/     # Zod schemas
├── hooks/               # 自定义 Hooks
├── types/               # TypeScript 类型
└── styles/              # 全局样式
```

### 5.2 状态管理
```typescript
// 使用 Zustand 进行客户端状态管理
interface AppState {
  // 用户状态
  user: User | null
  profile: Profile | null
  
  // UI 状态
  theme: 'light' | 'dark'
  sidebarOpen: boolean
  
  // 数据缓存
  projectsCache: Map<string, Project>
  
  // Actions
  setUser: (user: User | null) => void
  toggleTheme: () => void
  clearCache: () => void
}
```

### 5.3 数据获取策略
```typescript
// 服务端数据获取（推荐）
async function ProjectsPage({ searchParams }: PageProps) {
  // 在服务端获取数据
  const projects = await fetchProjects(searchParams)
  
  return <ProjectsList projects={projects} />
}

// 客户端数据获取（必要时）
function useProjects(filters: ProjectFilters) {
  return useSWR(
    ['projects', filters],
    () => fetchProjects(filters),
    {
      revalidateOnFocus: false,
      dedupingInterval: 60000, // 1分钟内去重
    }
  )
}
```

## 6. 性能优化

### 6.1 数据库查询优化
- 使用数据库视图减少 JOIN 操作
- 利用分区表提升历史数据查询性能
- 实施查询结果缓存（Redis 预留）
- 使用数据库连接池

### 6.2 前端性能优化
- 路由预加载和代码分割
- 图片懒加载和 WebP 格式
- 服务端组件优先，减少客户端 JS
- 使用 React.memo 和 useMemo 优化渲染

### 6.3 缓存策略
```typescript
// 缓存配置
const cacheConfig = {
  // 静态资源缓存
  static: {
    maxAge: 31536000, // 1年
    immutable: true,
  },
  
  // API 响应缓存
  api: {
    projects: {
      maxAge: 300, // 5分钟
      staleWhileRevalidate: 600,
    },
    stats: {
      maxAge: 3600, // 1小时
    },
  },
  
  // 页面缓存
  pages: {
    public: {
      maxAge: 86400, // 1天
    },
    private: {
      maxAge: 0,
      noStore: true,
    },
  },
}
```

## 7. 安全措施

### 7.1 输入验证
```typescript
// 使用 Zod 进行严格的输入验证
const projectFilterSchema = z.object({
  page: z.coerce.number().min(1).default(1),
  pageSize: z.coerce.number().min(10).max(100).default(20),
  dateFrom: z.string().datetime().optional(),
  dateTo: z.string().datetime().optional(),
  keyword: z.string().max(100).optional(),
  type: z.enum(['CONTRACT', 'GRANT', 'OTHER']).optional(),
})
```

### 7.2 安全头设置
```typescript
// next.config.js 安全头配置
const securityHeaders = [
  {
    key: 'X-DNS-Prefetch-Control',
    value: 'on'
  },
  {
    key: 'Strict-Transport-Security',
    value: 'max-age=63072000; includeSubDomains; preload'
  },
  {
    key: 'X-Frame-Options',
    value: 'SAMEORIGIN'
  },
  {
    key: 'X-Content-Type-Options',
    value: 'nosniff'
  },
  {
    key: 'Referrer-Policy',
    value: 'origin-when-cross-origin'
  },
  {
    key: 'Content-Security-Policy',
    value: ContentSecurityPolicy.replace(/\s{2,}/g, ' ').trim()
  }
]
```

### 7.3 API 安全
- 所有 API 端点进行身份验证
- 实施请求频率限制（每分钟 60 次）
- 敏感操作记录审计日志
- SQL 注入防护（参数化查询）

## 8. 监控与日志

### 8.1 日志分级
```typescript
enum LogLevel {
  DEBUG = 0,   // 开发调试信息
  INFO = 1,    // 一般信息
  WARN = 2,    // 警告信息
  ERROR = 3,   // 错误信息
  FATAL = 4,   // 致命错误
}

// 结构化日志
interface LogEntry {
  level: LogLevel
  timestamp: string
  message: string
  userId?: string
  requestId?: string
  metadata?: Record<string, any>
}
```

### 8.2 性能监控指标
- 页面加载时间（Core Web Vitals）
- API 响应时间分布
- 数据库查询性能
- 错误率和类型分布
- 用户行为分析

### 8.3 告警规则
- API 错误率 > 5%
- 数据库响应时间 > 1秒
- 内存使用率 > 80%
- 登录失败率异常

## 9. 部署流程

### 9.1 环境配置
```bash
# 环境变量
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
SUPABASE_SERVICE_KEY=
NEXT_PUBLIC_TURNSTILE_SITE_KEY=
TURNSTILE_SECRET_KEY=
```

### 9.2 CI/CD 流程
```yaml
# GitHub Actions 示例
name: Deploy to Production
on:
  push:
    branches: [main]
    
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: pnpm/action-setup@v2
      - run: pnpm install --frozen-lockfile
      - run: pnpm run lint
      - run: pnpm run type-check
      - run: pnpm run test
      - run: pnpm run build
      - uses: vercel/action@v28
```

### 9.3 部署检查清单
- [ ] 环境变量配置完整
- [ ] 数据库迁移执行成功
- [ ] RLS 策略部署正确
- [ ] 安全头配置生效
- [ ] 监控告警配置完成
- [ ] 备份策略已启用

## 10. 开发规范

### 10.1 代码规范
- 使用 ESLint + Prettier 统一代码风格
- 遵循 TypeScript 严格模式
- 组件命名使用 PascalCase
- 函数命名使用 camelCase
- 常量命名使用 UPPER_SNAKE_CASE

### 10.2 Git 工作流
- 主分支：main（生产环境）
- 开发分支：develop（测试环境）
- 功能分支：feature/功能名称
- 修复分支：fix/问题描述
- 提交信息遵循 Conventional Commits

### 10.3 测试策略
- 单元测试覆盖率 > 80%
- 集成测试覆盖关键流程
- E2E 测试覆盖用户场景
- 性能测试定期执行
</artifact>
</artifacts>

<artifacts>
<artifact identifier="hiddengov-cursor-rules" type="text/markdown" title="HiddenGov Cursor Rules">
# HiddenGov Project Cursor Rules

## Project Overview
You are working on HiddenGov, a B2B SaaS platform for U.S. government procurement data services. The platform provides efficient querying, analysis, and export of SAM.gov data through a closed registration system using invite codes.

## Tech Stack
- **Frontend**: Next.js 15 (App Router), TypeScript, Tailwind CSS, shadcn-ui
- **Backend**: Supabase (PostgreSQL, Auth, RLS)
- **Authentication**: Supabase Auth with Cloudflare Turnstile
- **Package Manager**: pnpm
- **Icons**: Lucide Icons
- **Validation**: Zod
- **Avatar**: DiceBear
- **Theme**: Light/Dark mode support

## Code Style and Naming Conventions

### File Naming
- Components: `PascalCase.tsx` (e.g., `ProjectList.tsx`)
- Pages: `page.tsx` in route folders
- Utilities: `camelCase.ts` (e.g., `formatDate.ts`)
- Types: `types.ts` or `[domain].types.ts`
- Hooks: `use[HookName].ts` (e.g., `useProjects.ts`)

### Code Organization
```typescript
// Import order
1. React/Next.js imports
2. Third-party library imports
3. Internal components
4. Internal utilities/hooks
5. Types
6. Styles/constants

// Example:
import { Suspense } from 'react'
import { createClient } from '@supabase/supabase-js'
import { Button } from '@/components/ui/button'
import { formatDate } from '@/lib/utils'
import type { Project } from '@/types/project'
```

### TypeScript Usage
```typescript
// Always use explicit types
interface Props {
  // Good: explicit and clear
  userId: string
  onUpdate: (data: ProjectData) => Promise<void>
  
  // Avoid: any or implicit types
  data: any // ❌
  callback: Function // ❌
}

// Use type for unions and intersections
type Status = 'active' | 'inactive' | 'pending'
type ProjectWithUser = Project & { user: User }

// Use interface for object shapes
interface User {
  id: string
  email: string
  profile: UserProfile
}
```

## Component Patterns

### Server Components (Default)
```typescript
// app/projects/page.tsx
export default async function ProjectsPage() {
  // Fetch data on server
  const supabase = createClient()
  const { data: projects } = await supabase
    .from('us_sam_gov_project_view')
    .select('*')
    .order('posted_date', { ascending: false })
  
  return (
    <div className="container mx-auto py-8">
      <h1 className="mb-6 text-3xl font-bold">项目列表</h1>
      <ProjectList projects={projects} />
    </div>
  )
}
```

### Client Components
```typescript
// components/features/project-filters.tsx
'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'

export function ProjectFilters() {
  const [filters, setFilters] = useState({})
  const router = useRouter()
  
  // Client-side interactivity
  const handleFilter = () => {
    // Update URL params
    router.push(`/projects?${new URLSearchParams(filters)}`)
  }
  
  return (
    // Component JSX
  )
}
```

## Supabase Integration

### Client Creation
```typescript
// lib/supabase/server.ts
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

export function createClient() {
  const cookieStore = cookies()
  
  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value
        },
      },
    }
  )
}

// For admin operations
export function createAdminClient() {
  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_KEY!, // Service key for admin
    { cookies: {} }
  )
}
```

### RLS Patterns
```typescript
// Always check user permissions
async function getProjects(userId: string) {
  const supabase = createClient()
  
  // RLS will automatically filter based on user
  const { data, error } = await supabase
    .from('us_sam_gov_project_view')
    .select('*')
  
  if (error) {
    console.error('Failed to fetch projects:', error)
    return []
  }
  
  return data
}
```

## Validation with Zod

### Schema Definition
```typescript
// lib/validations/project.ts
import { z } from 'zod'

export const projectFilterSchema = z.object({
  page: z.coerce.number().min(1).default(1),
  pageSize: z.coerce.number().min(10).max(100).default(20),
  keyword: z.string().max(100).optional(),
  dateFrom: z.string().datetime().optional(),
  dateTo: z.string().datetime().optional(),
  type: z.enum(['CONTRACT', 'GRANT', 'OTHER']).optional(),
  hasResources: z.coerce.boolean().optional(),
})

export type ProjectFilters = z.infer<typeof projectFilterSchema>
```

### Form Validation
```typescript
// Use with react-hook-form
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'

const form = useForm<ProjectFilters>({
  resolver: zodResolver(projectFilterSchema),
  defaultValues: {
    page: 1,
    pageSize: 20,
  },
})
```

## API Route Patterns

### Standard API Response
```typescript
// app/api/projects/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { projectFilterSchema } from '@/lib/validations'

export async function GET(request: NextRequest) {
  try {
    // Parse and validate query params
    const searchParams = Object.fromEntries(request.nextUrl.searchParams)
    const filters = projectFilterSchema.parse(searchParams)
    
    // Check authentication
    const supabase = createClient()
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }
    
    // Fetch data
    const { data, error, count } = await supabase
      .from('us_sam_gov_project_view')
      .select('*', { count: 'exact' })
      .range(
        (filters.page - 1) * filters.pageSize,
        filters.page * filters.pageSize - 1
      )
    
    if (error) throw error
    
    return NextResponse.json({
      success: true,
      data,
      pagination: {
        page: filters.page,
        pageSize: filters.pageSize,
        total: count || 0,
      },
    })
  } catch (error) {
    console.error('API Error:', error)
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : 'Internal server error' 
      },
      { status: 500 }
    )
  }
}
```

## UI Component Usage

### shadcn-ui Components
```typescript
// Always import from @/components/ui
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'

// Use with consistent styling
<Button variant="default" size="md" className="w-full sm:w-auto">
  确认
</Button>
```

### Dark Mode Support
```typescript
// Use Tailwind dark mode classes
<div className="bg-white dark:bg-gray-900">
  <h1 className="text-gray-900 dark:text-white">
    标题
  </h1>
</div>
```

## Error Handling

### Try-Catch Pattern
```typescript
// Consistent error handling
try {
  const result = await riskyOperation()
  return { success: true, data: result }
} catch (error) {
  // Log error details
  console.error('Operation failed:', {
    error,
    context: { userId, operation: 'riskyOperation' }
  })
  
  // Return user-friendly error
  return { 
    success: false, 
    error: '操作失败，请稍后重试' 
  }
}
```

### Error Boundaries
```typescript
// app/error.tsx
'use client'

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  return (
    <div className="flex min-h-screen items-center justify-center">
      <div className="text-center">
        <h2 className="mb-4 text-2xl font-bold">出错了</h2>
        <p className="mb-4 text-gray-600">{error.message}</p>
        <Button onClick={reset}>重试</Button>
      </div>
    </div>
  )
}
```

## Performance Optimization

### Data Fetching
```typescript
// Parallel data fetching
const [projects, stats] = await Promise.all([
  supabase.from('us_sam_gov_project_view').select('*'),
  supabase.from('us_sam_gov_stats').select('*'),
])

// Use select to fetch only needed fields
const { data } = await supabase
  .from('us_sam_gov_project_view')
  .select('id, title, posted_date, type')
```

### Memoization
```typescript
// Memoize expensive computations
const processedData = useMemo(() => {
  return heavyDataProcessing(rawData)
}, [rawData])

// Memoize components
const MemoizedComponent = memo(ExpensiveComponent)
```

## Security Best Practices

### Input Sanitization
```typescript
// Always validate and sanitize user input
const sanitizedInput = DOMPurify.sanitize(userInput)

// Use parameterized queries (Supabase handles this)
const { data } = await supabase
  .from('table')
  .select('*')
  .eq('column', userInput) // Safe from SQL injection
```

### Authentication Checks
```typescript
// Middleware pattern for protected routes
export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  
  // Check authentication for protected routes
  if (pathname.startsWith('/admin')) {
    const supabase = createClient()
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return NextResponse.redirect(new URL('/login', request.url))
    }
    
    // Check admin role
    const { data: profile } = await supabase
      .from('us_sam_u_profiles')
      .select('role')
      .eq('auth_user_id', user.id)
      .single()
    
    if (!['ADMIN', 'SUPER'].includes(profile?.role)) {
      return NextResponse.redirect(new URL('/403', request.url))
    }
  }
  
  return NextResponse.next()
}
```

## Testing Guidelines

### Component Testing
```typescript
// __tests__/components/project-list.test.tsx
import { render, screen } from '@testing-library/react'
import { ProjectList } from '@/components/features/project-list'

describe('ProjectList', () => {
  it('renders projects correctly', () => {
    const mockProjects = [
      { id: '1', title: 'Test Project', posted_date: '2024-01-01' }
    ]
    
    render(<ProjectList projects={mockProjects} />)
    
    expect(screen.getByText('Test Project')).toBeInTheDocument()
  })
})
```

## Comments and Documentation

### Code Comments
```typescript
/**
 * 获取项目列表
 * @param filters - 筛选条件
 * @returns 项目列表和分页信息
 */
async function getProjects(filters: ProjectFilters) {
  // 验证用户权限
  const hasAccess = await checkUserAccess()
  
  if (!hasAccess) {
    throw new Error('无权访问')
  }
  
  // 构建查询
  let query = supabase
    .from('us_sam_gov_project_view')
    .select('*', { count: 'exact' })
  
  // 应用筛选条件
  if (filters.keyword) {
    query = query.ilike('title', `%${filters.keyword}%`)
  }
  
  // ... rest of implementation
}
```

### JSDoc for Types
```typescript
/**
 * 用户档案信息
 */
interface UserProfile {
  /** 用户ID (关联 auth.users) */
  auth_user_id: string
  /** 用户角色 */
  role: 'USER' | 'ADMIN' | 'SUPER'
  /** 订阅开始时间 */
  subscription_start?: Date
  /** 订阅结束时间 */
  subscription_end?: Date
  /** 账户是否激活 */
  is_active: boolean
}
```

## Common Patterns

### Loading States
```typescript
// Use Suspense for server components
<Suspense fallback={<ProjectListSkeleton />}>
  <ProjectList />
</Suspense>

// Use loading state for client components
const [isLoading, setIsLoading] = useState(false)

if (isLoading) {
  return <Spinner />
}
```

### Toast Notifications
```typescript
import { toast } from 'sonner'

// Success notification
toast.success('操作成功')

// Error notification
toast.error('操作失败，请重试')

// With custom options
toast.success('数据已保存', {
  description: '您的更改已成功保存到数据库',
  duration: 5000,
})
```

## Environment Variables

### Required Variables
```env
# Public (exposed to client)
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
NEXT_PUBLIC_TURNSTILE_SITE_KEY=

# Server-only
SUPABASE_SERVICE_KEY=
TURNSTILE_SECRET_KEY=
```

### Usage Pattern
```typescript
// Always validate env vars
if (!process.env.NEXT_PUBLIC_SUPABASE_URL) {
  throw new Error('Missing NEXT_PUBLIC_SUPABASE_URL')
}

// Type-safe env access
declare global {
  namespace NodeJS {
    interface ProcessEnv {
      NEXT_PUBLIC_SUPABASE_URL: string
      NEXT_PUBLIC_SUPABASE_ANON_KEY: string
      SUPABASE_SERVICE_KEY: string
    }
  }
}
```

## Development Workflow

### Branch Naming
- Feature: `feature/user-management`
- Bug fix: `fix/login-error`
- Refactor: `refactor/api-structure`

### Commit Messages
Follow conventional commits:
- `feat: 添加用户管理功能`
- `fix: 修复登录错误`
- `docs: 更新 README`
- `style: 格式化代码`
- `refactor: 重构 API 结构`
- `test: 添加单元测试`
- `chore: 更新依赖`

### Pre-commit Checks
```bash
# Run before committing
pnpm lint        # ESLint
pnpm type-check  # TypeScript
pnpm test        # Tests
pnpm build       # Build check
```
</artifact>
</artifacts>

<artifacts>
<artifact identifier="hiddengov-directory-structure" type="text/markdown" title="HiddenGov 项目目录结构">
# HiddenGov 项目目录结构

```
hiddengov/
├── .github/                      # GitHub 配置
│   └── workflows/               # GitHub Actions
│       ├── ci.yml              # 持续集成
│       └── deploy.yml          # 部署流程
├── .husky/                      # Git hooks
│   ├── pre-commit              # 提交前检查
│   └── pre-push                # 推送前检查
├── public/                      # 静态资源
│   ├── favicon.ico             # 网站图标
│   └── robots.txt              # 爬虫规则
├── src/                         # 源代码目录
│   ├── app/                    # Next.js App Router
│   │   ├── (auth)/            # 认证页面组
│   │   │   ├── login/         # 登录页
│   │   │   │   ├── page.tsx
│   │   │   │   └── loading.tsx
│   │   │   ├── register/      # 注册页
│   │   │   │   └── page.tsx
│   │   │   ├── layout.tsx     # 认证布局
│   │   │   └── styles.css     # 认证样式
│   │   ├── (dashboard)/       # 主应用页面组
│   │   │   ├── projects/      # 项目管理
│   │   │   │   ├── page.tsx   # 项目列表
│   │   │   │   ├── [id]/      # 项目详情
│   │   │   │   │   └── page.tsx
│   │   │   │   └── loading.tsx
│   │   │   ├── archived/      # 历史数据
│   │   │   │   └── page.tsx
│   │   │   ├── profile/       # 个人中心
│   │   │   │   └── page.tsx
│   │   │   ├── stats/         # 数据统计
│   │   │   │   └── page.tsx
│   │   │   └── layout.tsx     # 仪表板布局
│   │   ├── admin/             # 管理后台
│   │   │   ├── users/         # 用户管理
│   │   │   │   ├── page.tsx
│   │   │   │   └── [id]/
│   │   │   │       └── page.tsx
│   │   │   ├── invite-codes/  # 邀请码管理
│   │   │   │   ├── page.tsx
│   │   │   │   └── create/
│   │   │   │       └── page.tsx
│   │   │   ├── logs/          # 日志查询
│   │   │   │   └── page.tsx
│   │   │   └── layout.tsx     # 管理布局
│   │   ├── api/               # API 路由
│   │   │   ├── auth/          # 认证相关
│   │   │   │   ├── register/
│   │   │   │   │   └── route.ts
│   │   │   │   ├── login/
│   │   │   │   │   └── route.ts
│   │   │   │   ├── logout/
│   │   │   │   │   └── route.ts
│   │   │   │   └── validate-code/
│   │   │   │       └── route.ts
│   │   │   ├── projects/      # 项目相关
│   │   │   │   ├── route.ts   # GET list
│   │   │   │   ├── [id]/
│   │   │   │   │   └── route.ts
│   │   │   │   └── export/
│   │   │   │       └── route.ts
│   │   │   ├── users/         # 用户相关
│   │   │   │   ├── profile/
│   │   │   │   │   └── route.ts
│   │   │   │   ├── list/
│   │   │   │   │   └── route.ts
│   │   │   │   └── [id]/
│   │   │   │       └── status/
│   │   │   │           └── route.ts
│   │   │   ├── invite-codes/  # 邀请码相关
│   │   │   │   ├── create/
│   │   │   │   │   └── route.ts
│   │   │   │   ├── list/
│   │   │   │   │   └── route.ts
│   │   │   │   └── [code]/
│   │   │   │       └── disable/
│   │   │   │           └── route.ts
│   │   │   └── stats/         # 统计相关
│   │   │       └── dashboard/
│   │   │           └── route.ts
│   │   ├── globals.css        # 全局样式
│   │   ├── layout.tsx         # 根布局
│   │   ├── page.tsx           # 首页
│   │   ├── not-found.tsx      # 404页面
│   │   └── error.tsx          # 错误页面
│   ├── components/            # 组件目录
│   │   ├── ui/               # shadcn-ui 组件
│   │   │   ├── button.tsx
│   │   │   ├── card.tsx
│   │   │   ├── dialog.tsx
│   │   │   ├── form.tsx
│   │   │   ├── input.tsx
│   │   │   ├── table.tsx
│   │   │   ├── toast.tsx
│   │   │   └── ...
│   │   ├── features/         # 业务组件
│   │   │   ├── auth/         # 认证相关
│   │   │   │   ├── login-form.tsx
│   │   │   │   ├── register-form.tsx
│   │   │   │   └── turnstile.tsx
│   │   │   ├── projects/     # 项目相关
│   │   │   │   ├── project-list.tsx
│   │   │   │   ├── project-filters.tsx
│   │   │   │   ├── project-detail.tsx
│   │   │   │   └── project-export.tsx
│   │   │   ├── users/        # 用户相关
│   │   │   │   ├── user-list.tsx
│   │   │   │   ├── user-profile.tsx
│   │   │   │   └── user-avatar.tsx
│   │   │   └── admin/        # 管理相关
│   │   │       ├── invite-code-form.tsx
│   │   │       └── log-viewer.tsx
│   │   └── layouts/          # 布局组件
│   │       ├── header.tsx    # 顶部导航
│   │       ├── sidebar.tsx   # 侧边栏
│   │       ├── footer.tsx    # 页脚
│   │       └── theme-toggle.tsx
│   ├── hooks/                # 自定义 Hooks
│   │   ├── use-auth.ts       # 认证相关
│   │   ├── use-projects.ts   # 项目数据
│   │   ├── use-theme.ts      # 主题切换
│   │   └── use-toast.ts      # 提示消息
│   ├── lib/                  # 工具库
│   │   ├── supabase/        # Supabase 相关
│   │   │   ├── client.ts    # 客户端
│   │   │   ├── server.ts    # 服务端
│   │   │   └── admin.ts     # 管理端
│   │   ├── validations/     # Zod schemas
│   │   │   ├── auth.ts      # 认证验证
│   │   │   ├── project.ts   # 项目验证
│   │   │   └── user.ts      # 用户验证
│   │   ├── utils/           # 工具函数
│   │   │   ├── format.ts    # 格式化
│   │   │   ├── export.ts    # 导出功能
│   │   │   └── constants.ts # 常量定义
│   │   └── api/             # API 工具
│   │       ├── client.ts    # API 客户端
│   │       └── errors.ts    # 错误处理
│   ├── middleware.ts         # Next.js 中间件
│   ├── stores/              # 状态管理
│   │   ├── auth.ts          # 认证状态
│   │   └── ui.ts            # UI 状态
│   └── types/               # TypeScript 类型
│       ├── database.ts      # 数据库类型
│       ├── api.ts           # API 类型
│       └── index.ts         # 导出类型
├── tests/                       # 测试文件
│   ├── unit/                   # 单元测试
│   ├── integration/            # 集成测试
│   └── e2e/                    # 端到端测试
├── .env.example                # 环境变量示例
├── .env.local                  # 本地环境变量
├── .eslintrc.json              # ESLint 配置
├── .gitignore                  # Git 忽略文件
├── .prettierrc                 # Prettier 配置
├── components.json             # shadcn-ui 配置
├── next-env.d.ts               # Next.js 类型
├── next.config.js              # Next.js 配置
├── package.json                # 项目依赖
├── pnpm-lock.yaml              # 依赖锁定文件
├── postcss.config.js           # PostCSS 配置
├── README.md                   # 项目说明
├── tailwind.config.ts          # Tailwind 配置
└── tsconfig.json               # TypeScript 配置
```

## 目录说明

### 核心目录

1. **`src/app/`** - Next.js 15 App Router 的核心目录
   - 使用路由组 `(auth)`, `(dashboard)`, `admin` 组织页面
   - 每个路由文件夹包含 `page.tsx` 作为页面组件
   - `loading.tsx` 和 `error.tsx` 处理加载和错误状态

2. **`src/components/`** - 可复用组件
   - `ui/` - shadcn-ui 基础组件
   - `features/` - 业务功能组件
   - `layouts/` - 页面布局组件

3. **`src/lib/`** - 核心工具和配置
   - `supabase/` - 数据库连接配置
   - `validations/` - Zod 验证模式
   - `utils/` - 通用工具函数

4. **`src/hooks/`** - 自定义 React Hooks
   - 封装常用的状态逻辑
   - 提供数据获取和操作接口

5. **`src/types/`** - TypeScript 类型定义
   - 统一管理类型定义
   - 确保类型安全

### 配置文件

- **`middleware.ts`** - 处理认证和权限控制
- **`components.json`** - shadcn-ui 组件配置
- **`tailwind.config.ts`** - Tailwind CSS 配置，包括暗色模式
- **`next.config.js`** - Next.js 配置，包括安全头设置

### 开发工具

- **`.husky/`** - Git hooks 自动化代码检查
- **`.github/workflows/`** - CI/CD 自动化流程
- **`tests/`** - 完整的测试套件

这个目录结构遵循 Next.js 15 的最佳实践，充分利用 App Router 的特性，同时保持代码的模块化和可维护性。
</artifact>
</artifacts>

我已经为 HiddenGov 项目创建了完整的文档集，包括：

## 1. **产品需求文档 (PRD) - 完整版**
详细定义了平台的功能需求、用户角色、界面设计和项目计划。根据您的反馈，我已经明确了：
- B2B 封闭平台定位
- CSV 导出功能（一期实现）
- 管理功能范围（用户管理、邀请码管理、日志查询）
- 查询和导出限制作为二期内容

## 2. **技术设计文档 (TDD)**
深入阐述了系统架构、技术实现细节，包括：
- 基于 Next.js 15 App Router 的 SSR 架构
- Supabase RLS 权限设计
- API 设计规范和错误处理
- 性能优化策略
- 安全措施实现

## 3. **Cursor Rules**
为 AI 编程助手提供了详细的开发规范，涵盖：
- 项目技术栈和命名规范
- 组件开发模式（Server/Client Components）
- Supabase 集成最佳实践
- 类型安全和验证规范
- 详细的代码示例和注释要求

## 4. **项目目录结构**
基于 Next.js 15 最佳实践设计的完整目录结构，特点包括：
- 使用路由组组织页面（认证、仪表板、管理后台）
- 清晰的组件分层（UI组件、业务组件、布局组件）
- 完善的类型定义和工具函数组织
- 配置文件和开发工具的合理安排

这套文档为 HiddenGov 项目提供了从需求到实现的完整指导。您可以基于这些文档开始项目开发，每个文档都考虑了您提到的技术栈要求和最佳实践。

需要我对某个特定部分进行调整或补充吗？
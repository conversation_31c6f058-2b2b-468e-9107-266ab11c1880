# 用户状态与登录联动最佳实践

## 1. 需求与目标
- 用户表（us_sam_u_profiles）有 status 字段（active/expired/suspended）。
- 停用/过期用户无法登录，登录时有明确提示。
- 保障体验与安全，兼顾前端友好与后端/数据库安全。

## 2. 方案一：前端校验（推荐，体验好）
- 登录成功后，前端用 user.id 查询 profile.status。
- 若 status !== 'active'，立即 signOut 并弹窗提示。
- 适合大部分业务场景，体验流畅。

### 前端实现代码示例
```ts
const { data, error } = await supabase.auth.signInWithPassword({ email, password });
if (error) {
  showError(error.message);
  return;
}
const userId = data.user.id;
const { data: profile } = await supabase
  .from('us_sam_u_profiles')
  .select('status')
  .eq('auth_user_id', userId)
  .single();
if (!profile || profile.status !== 'active') {
  await supabase.auth.signOut();
  showError(profile?.status === 'expired' ? 'Account expired' : 'Account suspended');
  return;
}
// 正常登录
```

## 3. 方案二：后端API校验（高安全场景）
- 登录API代理，登录后查 profile.status，非 active 则返回错误。
- 适合管理后台、敏感操作等高安全需求。

## 4. 方案三：RLS策略加固（推荐与前端方案配合）
- 所有业务表加"只允许active用户"RLS策略，彻底杜绝绕过。
- 可用 SQL 函数复用，极简维护。

### RLS策略SQL示例
```sql
-- 1. 定义复用函数
CREATE FUNCTION is_active_user() RETURNS boolean AS $$
  SELECT EXISTS (
    SELECT 1 FROM us_sam_u_profiles WHERE auth_user_id = auth.uid() AND status = 'active'
  );
$$ LANGUAGE sql STABLE;

-- 2. 各业务表策略
CREATE POLICY "Only active users" ON your_table
FOR ALL
 USING (is_active_user())
 WITH CHECK (is_active_user());
```

## 5. 总结
- 推荐前端校验+RLS双重保障，体验与安全兼得。
- RLS策略用函数复用，维护极简。
- 团队开发时务必知晓：前端校验提升体验，RLS/后端校验才是最终安全边界。

## 6. Supabase会话安全设置（补充）
- Supabase控制台Authentication > User Sessions可配置全局会话有效期（Time-box user sessions）、不活跃超时（Inactivity timeout）、单用户单会话等安全策略。
- 合理配置可实现"过期后强制重新登录"，提升整体安全。
- 推荐高安全场景下结合RLS策略与会话设置，确保即使用户未主动登出也无法访问受保护数据。

## 7. 团队培训与落地建议

### 核心知识点
- 用户状态（status）与登录、数据访问的联动机制
- 前端校验与后端/RLS双重保障的安全边界
- Supabase会话有效期与安全策略配置
- shadcn/ui分页、排序、批量操作等现代UI最佳实践

### 常见误区
- 仅靠前端校验无法防止API被绕过，RLS/后端校验才是最终安全保障
- 忽视会话有效期设置，导致过期/停用用户长时间持有有效session
- 分页、排序参数未联动后端，导致表格内容与预期不符

### 落地流程建议
1. 新成员先通读 docs/chat.log 和 docs/user-status-login.md，理解整体架构与安全链路
2. 本地运行项目，体验前端分页、排序、批量创建、登录校验等功能
3. 结合 Supabase 控制台，实际配置 User Sessions、RLS 策略，理解其作用
4. 通过代码审查和小组讨论，确保所有新功能都遵循最佳实践

### 团队协作与知识传承
- 重要决策、最佳实践、踩坑经验务必同步到 chat.log 和相关 md 文档
- 代码注释与文档严格中英文分离，便于国际化和团队协作
- 定期组织技术分享，轮流讲解安全链路、RLS、UI最佳实践等主题

### 培训建议
- 新成员入职首周安排"后台管理最佳实践"专题培训，结合实际代码演示
- 组织RLS策略实战演练，模拟不同用户状态下的数据访问效果
- 定期回顾和更新文档，确保知识库与实际项目同步

# 用户行为日志（us_sam_u_logs）设计与实践文档

## 1. 表结构设计
- 表名：public.us_sam_u_logs
- 主要字段：
  - id：主键 UUID
  - auth_user_id：用户ID，外键关联 auth.users(id) 和 us_sam_u_profiles(auth_user_id)
  - event_type：行为类型（如 login、logout、register、download）
  - event_time：行为发生时间（timestamptz，默认 now()）
  - ip_address：行为发生时的IP（后端自动采集）
  - user_agent：设备/浏览器信息
  - session_id：会话ID
  - referrer：来源页面URL
  - page_url：当前页面URL
  - duration：停留时长（秒）
  - extra：扩展信息（jsonb，已不在前端展示）
- 主要索引：auth_user_id、event_type、event_time、session_id
- 外键约束：auth_user_id 同时关联 auth.users 和 us_sam_u_profiles，便于联表查邮箱

## 2. RLS 与安全策略
- 日志表默认仅允许本人或管理员查询、写入自己的日志
- 后端管理 API 采用 service key，无视 RLS，前端埋点和用户自查走 RLS
- 通过 JWT + service key + role 校验，确保只有 admin 能查全量日志

## 3. 后端 API 设计
- /api/user/logs：POST，前端埋点写入，自动补全 IP，需带用户 access_token
- /api/sysop/logs：GET，后台管理用，支持分页、搜索、排序、条件筛选，仅 admin 可查
- 查询支持 event_type、auth_user_id、模糊搜索、时间范围等

## 4. 前端埋点集成
- 登录、注册、登出等关键行为，均在成功后调用 logEvent 工具函数写入日志
- logEvent 自动采集 user_agent、page_url，access_token 由 Supabase session 获取
- 后端自动补全 ip_address，无需前端传递

## 5. 后台管理页面
- sysop 页面新增"用户日志"Tab，表格字段：邮箱、事件类型、事件时间、IP、页面URL、来源、UA
- 支持分页、排序、模糊搜索，数据实时拉取
- Extra 字段已移除，UA 放在最后一列

## 6. 典型用例与最佳实践
- 日志表可用于安全审计、风控、用户行为分析、异常追踪等
- 建议定期备份日志表，防止数据丢失
- 生产环境建议开启日志表归档、分区等优化措施

## 7. 常见问题
- 若联表查邮箱报"Could not find a relationship"，需为 auth_user_id 增加 profiles 外键
- 本地开发时 IP 可能为 127.0.0.1，线上自动采集真实公网 IP 
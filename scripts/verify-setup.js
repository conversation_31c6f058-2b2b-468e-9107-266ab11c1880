#!/usr/bin/env node

// 项目设置验证脚本
const fs = require('fs')
const path = require('path')

console.log('🔍 验证 HiddenGov 项目设置...\n')

const checks = [
  {
    name: '检查 package.json',
    check: () => fs.existsSync('package.json'),
    message: 'package.json 文件存在'
  },
  {
    name: '检查 Next.js 配置',
    check: () => fs.existsSync('next.config.ts'),
    message: 'Next.js 配置文件存在'
  },
  {
    name: '检查 TypeScript 配置',
    check: () => fs.existsSync('tsconfig.json'),
    message: 'TypeScript 配置文件存在'
  },
  {
    name: '检查 Tailwind CSS',
    check: () => {
      // Tailwind 4 使用 @import "tailwindcss"
      if (fs.existsSync('src/app/globals.css')) {
        const content = fs.readFileSync('src/app/globals.css', 'utf8')
        return content.includes('@import "tailwindcss"')
      }
      return false
    },
    message: 'Tailwind CSS 配置正确'
  },
  {
    name: '检查环境变量模板',
    check: () => fs.existsSync('.env.example'),
    message: '环境变量模板文件存在'
  },
  {
    name: '检查 src 目录结构',
    check: () => {
      const dirs = [
        'src/app',
        'src/components',
        'src/lib',
        'src/types',
        'src/hooks'
      ]
      return dirs.every(dir => fs.existsSync(dir))
    },
    message: '核心目录结构完整'
  },
  {
    name: '检查认证页面',
    check: () => {
      return fs.existsSync('src/app/(auth)/login/page.tsx') &&
             fs.existsSync('src/app/(auth)/register/page.tsx')
    },
    message: '认证页面文件存在'
  },
  {
    name: '检查仪表板页面',
    check: () => {
      return fs.existsSync('src/app/(dashboard)/projects/page.tsx')
    },
    message: '仪表板页面文件存在'
  },
  {
    name: '检查 UI 组件',
    check: () => {
      const components = [
        'src/components/ui/button.tsx',
        'src/components/ui/card.tsx',
        'src/components/ui/form.tsx',
        'src/components/ui/input.tsx'
      ]
      return components.every(comp => fs.existsSync(comp))
    },
    message: 'UI 组件文件存在'
  },
  {
    name: '检查类型定义',
    check: () => {
      return fs.existsSync('src/types/database.ts') &&
             fs.existsSync('src/types/api.ts')
    },
    message: '类型定义文件存在'
  },
  {
    name: '检查 Supabase 配置',
    check: () => {
      return fs.existsSync('src/lib/supabase/client.ts') &&
             fs.existsSync('src/lib/supabase/server.ts')
    },
    message: 'Supabase 配置文件存在'
  },
  {
    name: '检查验证模式',
    check: () => {
      return fs.existsSync('src/lib/validations/auth.ts') &&
             fs.existsSync('src/lib/validations/project.ts')
    },
    message: '验证模式文件存在'
  },
  {
    name: '检查中间件',
    check: () => fs.existsSync('src/middleware.ts'),
    message: '权限中间件文件存在'
  }
]

let passed = 0
let failed = 0

checks.forEach(({ name, check, message }) => {
  try {
    if (check()) {
      console.log(`✅ ${message}`)
      passed++
    } else {
      console.log(`❌ ${name} 失败`)
      failed++
    }
  } catch (error) {
    console.log(`❌ ${name} 检查出错: ${error.message}`)
    failed++
  }
})

console.log(`\n📊 验证结果: ${passed} 通过, ${failed} 失败`)

if (failed === 0) {
  console.log('\n🎉 项目设置验证通过！')
  console.log('\n下一步操作:')
  console.log('1. 配置 Supabase 项目')
  console.log('2. 设置环境变量 (.env.local)')
  console.log('3. 运行 pnpm dev 启动开发服务器')
} else {
  console.log('\n⚠️  项目设置存在问题，请检查失败的项目')
  process.exit(1)
}
